/**
 * Persona Analytics Component
 * 
 * Displays performance metrics and insights for brainstorming personas
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Star,
  Clock,
  Users,
  Lightbulb,
  Target,
  Award,
  Activity,
  Calendar,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { cn } from '@/lib/utils';
import { usePersonaStore } from '@/stores/personaStore';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';
import { BrainstormingPersona, PersonaAnalytics as PersonaAnalyticsType } from '@/types/persona';

interface PersonaAnalyticsProps {
  className?: string;
  timeframe?: 'day' | 'week' | 'month' | 'all';
  onTimeframeChange?: (timeframe: 'day' | 'week' | 'month' | 'all') => void;
}

export const PersonaAnalytics: React.FC<PersonaAnalyticsProps> = ({
  className,
  timeframe = 'week',
  onTimeframeChange
}) => {
  const { theme } = useBrainstormingTheme();
  const { personas, getTopPerformingPersonas } = usePersonaStore();

  const [selectedPersonaId, setSelectedPersonaId] = useState<string | null>(null);

  const allPersonas = Object.values(personas);
  const topPerformers = getTopPerformingPersonas(5);
  const selectedPersona = selectedPersonaId ? personas[selectedPersonaId] : null;

  // Mock analytics data - in a real app, this would come from the backend
  const generateMockAnalytics = (persona: BrainstormingPersona): PersonaAnalyticsType => ({
    personaId: persona.id,
    timeframe,
    metrics: {
      usage_frequency: Math.random() * 100,
      idea_generation_rate: Math.random() * 10,
      collaboration_score: Math.random() * 100,
      effectiveness_rating: Math.random() * 5,
      session_impact: Math.random() * 100,
      user_satisfaction: Math.random() * 5
    },
    trends: {
      usage_over_time: Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        count: Math.floor(Math.random() * 20)
      })),
      effectiveness_over_time: Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        score: Math.random() * 5
      })),
      idea_quality_trend: Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        quality: Math.random() * 5
      }))
    },
    comparisons: {
      vs_other_personas: {},
      vs_baseline: Math.random() * 2 - 1 // -1 to 1
    }
  });

  const MetricCard: React.FC<{
    title: string;
    value: string | number;
    change?: number;
    icon: React.ComponentType<any>;
    color?: string;
  }> = ({ title, value, change, icon: Icon, color = theme.colors.primary }) => (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold" style={{ color }}>
              {typeof value === 'number' ? value.toFixed(1) : value}
            </p>
            {change !== undefined && (
              <div className="flex items-center gap-1 mt-1">
                {change > 0 ? (
                  <TrendingUp className="w-3 h-3 text-green-500" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-500" />
                )}
                <span className={cn(
                  "text-xs",
                  change > 0 ? "text-green-500" : "text-red-500"
                )}>
                  {Math.abs(change).toFixed(1)}%
                </span>
              </div>
            )}
          </div>
          <Icon className="w-8 h-8 opacity-60" style={{ color }} />
        </div>
      </CardContent>
    </Card>
  );

  const PersonaRankingCard: React.FC<{ persona: BrainstormingPersona; rank: number }> = ({ 
    persona, 
    rank 
  }) => {
    const analytics = generateMockAnalytics(persona);
    
    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        className="cursor-pointer"
        onClick={() => setSelectedPersonaId(persona.id)}
      >
        <Card className={cn(
          "transition-all duration-200",
          selectedPersonaId === persona.id && "ring-2 ring-offset-2"
        )}
        style={{
          ringColor: selectedPersonaId === persona.id ? theme.colors.primary : 'transparent'
        }}>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-bold text-sm">
                {rank}
              </div>
              
              <div 
                className="w-10 h-10 rounded-full flex items-center justify-center"
                style={{ backgroundColor: persona.color + '20' }}
              >
                <div className="w-5 h-5" style={{ color: persona.color }}>
                  {/* Icon would go here */}
                </div>
              </div>

              <div className="flex-1 min-w-0">
                <h4 className="font-medium truncate">{persona.name}</h4>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Star className="w-3 h-3" />
                  <span>{analytics.metrics.effectiveness_rating.toFixed(1)}</span>
                  <span>•</span>
                  <span>{analytics.metrics.usage_frequency.toFixed(0)} uses</span>
                </div>
              </div>

              <div className="text-right">
                <div className="text-sm font-medium" style={{ color: theme.colors.primary }}>
                  {analytics.metrics.idea_generation_rate.toFixed(1)}
                </div>
                <div className="text-xs text-muted-foreground">ideas/session</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold" style={{ color: theme.colors.text }}>
            Persona Analytics
          </h2>
          <p style={{ color: theme.colors.textSecondary }}>
            Track performance and insights across your brainstorming personas
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Select value={timeframe} onValueChange={onTimeframeChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Today</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>

          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="rankings">Rankings</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <MetricCard
              title="Total Personas"
              value={allPersonas.length}
              icon={Users}
              color={theme.colors.primary}
            />
            <MetricCard
              title="Active Sessions"
              value={Math.floor(Math.random() * 50)}
              change={Math.random() * 20 - 10}
              icon={Activity}
              color={theme.colors.success}
            />
            <MetricCard
              title="Ideas Generated"
              value={allPersonas.reduce((sum, p) => sum + p.metrics.ideasGenerated, 0)}
              change={Math.random() * 30 - 15}
              icon={Lightbulb}
              color={theme.colors.warning}
            />
            <MetricCard
              title="Avg. Rating"
              value={allPersonas.reduce((sum, p) => sum + p.metrics.averageRating, 0) / allPersonas.length || 0}
              change={Math.random() * 10 - 5}
              icon={Star}
              color={theme.colors.accent}
            />
          </div>

          {/* Top Performers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-5 h-5" style={{ color: theme.colors.primary }} />
                Top Performing Personas
              </CardTitle>
              <CardDescription>
                Based on effectiveness rating and usage frequency
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {topPerformers.map((persona, index) => (
                <PersonaRankingCard
                  key={persona.id}
                  persona={persona}
                  rank={index + 1}
                />
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Rankings Tab */}
        <TabsContent value="rankings" className="space-y-6">
          <div className="space-y-3">
            {allPersonas
              .sort((a, b) => b.metrics.averageRating - a.metrics.averageRating)
              .map((persona, index) => (
                <PersonaRankingCard
                  key={persona.id}
                  persona={persona}
                  rank={index + 1}
                />
              ))}
          </div>
        </TabsContent>

        {/* Trends Tab */}
        <TabsContent value="trends" className="space-y-6">
          <div className="text-center py-12" style={{ color: theme.colors.textSecondary }}>
            <BarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <h3 className="font-medium mb-2">Trend Analysis Coming Soon</h3>
            <p>Detailed trend charts and analytics will be available here</p>
          </div>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <div className="text-center py-12" style={{ color: theme.colors.textSecondary }}>
            <Target className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <h3 className="font-medium mb-2">AI Insights Coming Soon</h3>
            <p>Intelligent recommendations and insights will be available here</p>
          </div>
        </TabsContent>
      </Tabs>

      {/* Selected Persona Details */}
      {selectedPersona && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div 
                className="w-6 h-6 rounded-full"
                style={{ backgroundColor: selectedPersona.color }}
              />
              {selectedPersona.name} - Detailed Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8" style={{ color: theme.colors.textSecondary }}>
              Detailed persona analytics coming soon...
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
