/**
 * Template-Based Session Starter
 * 
 * Provides guided session creation with pre-configured templates for different
 * brainstorming methodologies like SWOT, Design Thinking, Five Whys, etc.
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Grid3x3,
  <PERSON><PERSON>,
  HelpCircle,
  FileText,
  Target,
  Users,
  Clock,
  ArrowRight,
  ArrowLeft,
  Check,
  Lightbulb,
  Brain,
  Zap,
  Layers,
  Compass,
  Loader2,
  AlertCircle
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';
import { TemplateType } from '@/types/brainstorm';

interface BrainstormingTemplate {
  id: TemplateType;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  duration: string;
  participants: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  steps: TemplateStep[];
  prompts: string[];
  goals: string[];
}

interface TemplateStep {
  id: string;
  title: string;
  description: string;
  duration: string;
  instructions: string[];
}

const BRAINSTORMING_TEMPLATES: BrainstormingTemplate[] = [
  {
    id: TemplateType.SWOT,
    name: 'SWOT Analysis',
    description: 'Analyze Strengths, Weaknesses, Opportunities, and Threats',
    icon: Grid3x3,
    color: '#3B82F6',
    duration: '45-60 minutes',
    participants: '3-8 people',
    difficulty: 'Beginner',
    steps: [
      {
        id: 'strengths',
        title: 'Identify Strengths',
        description: 'What advantages do you have?',
        duration: '10 minutes',
        instructions: [
          'List internal positive attributes',
          'Consider unique resources and capabilities',
          'Think about what you do better than competitors'
        ]
      },
      {
        id: 'weaknesses',
        title: 'Identify Weaknesses',
        description: 'What could be improved?',
        duration: '10 minutes',
        instructions: [
          'List internal negative factors',
          'Consider resource limitations',
          'Think about areas for improvement'
        ]
      },
      {
        id: 'opportunities',
        title: 'Identify Opportunities',
        description: 'What external factors could help?',
        duration: '10 minutes',
        instructions: [
          'Look for external positive factors',
          'Consider market trends',
          'Think about emerging technologies'
        ]
      },
      {
        id: 'threats',
        title: 'Identify Threats',
        description: 'What external factors could harm?',
        duration: '10 minutes',
        instructions: [
          'List external negative factors',
          'Consider competitive threats',
          'Think about regulatory changes'
        ]
      }
    ],
    prompts: [
      'What unique advantages does our organization have?',
      'Where do we need to improve most urgently?',
      'What market opportunities should we pursue?',
      'What external threats should we prepare for?'
    ],
    goals: [
      'Comprehensive situational analysis',
      'Strategic planning foundation',
      'Risk identification',
      'Opportunity recognition'
    ]
  },
  {
    id: TemplateType.DESIGN_THINKING,
    name: 'Design Thinking',
    description: 'Human-centered approach to innovation and problem-solving',
    icon: Palette,
    color: '#8B5CF6',
    duration: '90-120 minutes',
    participants: '4-10 people',
    difficulty: 'Intermediate',
    steps: [
      {
        id: 'empathize',
        title: 'Empathize',
        description: 'Understand the user and their needs',
        duration: '20 minutes',
        instructions: [
          'Interview potential users',
          'Observe user behavior',
          'Create empathy maps'
        ]
      },
      {
        id: 'define',
        title: 'Define',
        description: 'Frame the problem statement',
        duration: '15 minutes',
        instructions: [
          'Synthesize observations',
          'Define core problems',
          'Create point-of-view statements'
        ]
      },
      {
        id: 'ideate',
        title: 'Ideate',
        description: 'Generate creative solutions',
        duration: '30 minutes',
        instructions: [
          'Brainstorm without judgment',
          'Build on others\' ideas',
          'Aim for quantity over quality'
        ]
      },
      {
        id: 'prototype',
        title: 'Prototype',
        description: 'Build quick, testable solutions',
        duration: '25 minutes',
        instructions: [
          'Create low-fidelity prototypes',
          'Focus on key features',
          'Make it testable'
        ]
      },
      {
        id: 'test',
        title: 'Test',
        description: 'Validate solutions with users',
        duration: '20 minutes',
        instructions: [
          'Test with real users',
          'Gather feedback',
          'Iterate based on learnings'
        ]
      }
    ],
    prompts: [
      'Who are we designing for and what do they need?',
      'What is the core problem we\'re trying to solve?',
      'How might we solve this problem creatively?',
      'What would a simple solution look like?'
    ],
    goals: [
      'User-centered solutions',
      'Creative problem solving',
      'Rapid prototyping',
      'Validated learning'
    ]
  },
  {
    id: TemplateType.FIVE_WHYS,
    name: 'Five Whys',
    description: 'Root cause analysis through iterative questioning',
    icon: HelpCircle,
    color: '#F59E0B',
    duration: '30-45 minutes',
    participants: '2-6 people',
    difficulty: 'Beginner',
    steps: [
      {
        id: 'problem',
        title: 'Define the Problem',
        description: 'Clearly state the issue',
        duration: '5 minutes',
        instructions: [
          'Write down the specific problem',
          'Ensure everyone understands it',
          'Keep it factual, not interpretive'
        ]
      },
      {
        id: 'why1',
        title: 'First Why',
        description: 'Why did this problem occur?',
        duration: '8 minutes',
        instructions: [
          'Ask why the problem happened',
          'Look for immediate causes',
          'Avoid assumptions'
        ]
      },
      {
        id: 'why2',
        title: 'Second Why',
        description: 'Why did that cause occur?',
        duration: '8 minutes',
        instructions: [
          'Dig deeper into the first answer',
          'Look for underlying factors',
          'Stay focused on facts'
        ]
      },
      {
        id: 'why3',
        title: 'Third Why',
        description: 'Continue the analysis',
        duration: '8 minutes',
        instructions: [
          'Keep digging deeper',
          'Look for systemic issues',
          'Consider multiple perspectives'
        ]
      },
      {
        id: 'solution',
        title: 'Identify Solutions',
        description: 'Address the root cause',
        duration: '8 minutes',
        instructions: [
          'Focus on the deepest root cause',
          'Brainstorm preventive measures',
          'Create actionable solutions'
        ]
      }
    ],
    prompts: [
      'What exactly is the problem we\'re investigating?',
      'Why do you think this happened?',
      'What evidence supports this cause?',
      'How can we prevent this from happening again?'
    ],
    goals: [
      'Root cause identification',
      'Problem prevention',
      'Systematic thinking',
      'Actionable solutions'
    ]
  },
  {
    id: TemplateType.CANVAS,
    name: 'Business Model Canvas',
    description: 'Visual tool for developing new or documenting existing business models',
    icon: Grid3x3,
    color: '#10B981', 
    duration: '60-90 minutes',
    participants: '4-8 people',
    difficulty: 'Intermediate',
    steps: [
      {
        id: 'value-propositions',
        title: 'Value Propositions',
        description: 'What value do we deliver?',
        duration: '15 minutes',
        instructions: [
          'Define unique value for each customer segment',
          'Consider functional, emotional, and social gains',
          'Address customer pains and needs'
        ]
      },
      {
        id: 'customer-segments',
        title: 'Customer Segments',
        description: 'Who are our most important customers?',
        duration: '15 minutes',
        instructions: [
          'Identify distinct customer groups',
          'Consider different needs and behaviors',
          'Prioritize segments by importance'
        ]
      },
      {
        id: 'channels',
        title: 'Channels',
        description: 'How do we deliver value?',
        duration: '10 minutes',
        instructions: [
          'Map customer touchpoints',
          'Consider awareness, evaluation, purchase phases',
          'Plan for after-sales support'
        ]
      },
      {
        id: 'revenue-costs',
        title: 'Revenue & Costs',
        description: 'What are the financial considerations?',
        duration: '20 minutes',
        instructions: [
          'Define revenue streams',
          'Identify key cost structures',
          'Consider pricing models'
        ]
      }
    ],
    prompts: [
      'What unique value do we offer to each customer segment?',
      'How do we reach and serve our customers?',
      'What resources and activities are most important?',
      'How do we generate revenue sustainably?'
    ],
    goals: [
      'Complete business model visualization',
      'Strategic alignment',
      'Revenue optimization',
      'Resource planning'
    ]
  },
  {
    id: TemplateType.CUSTOM,
    name: 'Creative Brainstorming Sprint',
    description: 'Fast-paced idea generation session for creative problem solving',
    icon: Zap,
    color: '#EF4444',
    duration: '45-60 minutes', 
    participants: '3-10 people',
    difficulty: 'Beginner',
    steps: [
      {
        id: 'warm-up',
        title: 'Warm-up Exercise',
        description: 'Get creative juices flowing',
        duration: '10 minutes',
        instructions: [
          'Quick word association game',
          'Share random creative ideas',
          'Build energy and openness'
        ]
      },
      {
        id: 'divergent',
        title: 'Divergent Thinking',
        description: 'Generate as many ideas as possible',
        duration: '20 minutes',
        instructions: [
          'No judgment or criticism',
          'Quantity over quality',
          'Build on others\' ideas',
          'Encourage wild ideas'
        ]
      },
      {
        id: 'convergent',
        title: 'Convergent Thinking',
        description: 'Cluster and refine ideas',
        duration: '15 minutes',
        instructions: [
          'Group similar ideas together',
          'Identify most promising concepts',
          'Combine and improve ideas'
        ]
      }
    ],
    prompts: [
      'What if there were no constraints?',
      'How might we approach this differently?',
      'What would [famous person] do?',
      'What\'s the craziest idea we could try?'
    ],
    goals: [
      'Maximum idea generation',
      'Creative breakthrough',
      'Team collaboration',
      'Innovative solutions'
    ]
  },
  {
    id: 'mindmap' as TemplateType,
    name: 'Mind Mapping Session',
    description: 'Visual exploration of ideas and their relationships',
    icon: Brain,
    color: '#8B5CF6',
    duration: '30-45 minutes',
    participants: '2-8 people',
    difficulty: 'Beginner',
    steps: [
      {
        id: 'central-topic',
        title: 'Define Central Topic', 
        description: 'Place the main topic at the center',
        duration: '5 minutes',
        instructions: [
          'Write the main topic in the center',
          'Make it clear and specific',
          'Ensure everyone understands the focus'
        ]
      },
      {
        id: 'main-branches',
        title: 'Create Main Branches',
        description: 'Add primary categories or themes',
        duration: '10 minutes',
        instructions: [
          'Draw branches from the central topic',
          'Use different colors for each branch',
          'Keep branch labels short and clear'
        ]
      },
      {
        id: 'sub-branches',
        title: 'Develop Sub-branches',
        description: 'Add details and connections',
        duration: '15 minutes',
        instructions: [
          'Add sub-branches with specific ideas',
          'Use keywords rather than sentences',
          'Show relationships between ideas'
        ]
      },
      {
        id: 'review-connect',
        title: 'Review and Connect',
        description: 'Look for patterns and new connections',
        duration: '5 minutes',
        instructions: [
          'Step back and view the whole map',
          'Identify unexpected connections',
          'Add new links between branches'
        ]
      }
    ],
    prompts: [
      'What are the main aspects of this topic?',
      'How do these ideas relate to each other?',
      'What additional details can we add?',
      'What patterns do we see emerging?'
    ],
    goals: [
      'Visual idea organization',
      'Relationship mapping',
      'Creative connections',
      'Comprehensive exploration'
    ]
  }
];

interface TemplateBasedSessionStarterProps {
  onSessionCreate: (config: SessionConfig) => void;
  onCancel: () => void;
  className?: string;
}

interface SessionConfig {
  title: string;
  description: string;
  template: TemplateType;
  goals: string[];
  participants: number;
  duration: number;
  tags: string[];
}

export const TemplateBasedSessionStarter: React.FC<TemplateBasedSessionStarterProps> = ({
  onSessionCreate,
  onCancel,
  className
}) => {
  const { theme } = useBrainstormingTheme();
  const [step, setStep] = useState<'template' | 'configure' | 'review'>('template');
  const [selectedTemplate, setSelectedTemplate] = useState<BrainstormingTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [sessionConfig, setSessionConfig] = useState<Partial<SessionConfig>>({
    title: '',
    description: '',
    goals: [],
    participants: 5,
    duration: 60,
    tags: []
  });

  const handleTemplateSelect = (template: BrainstormingTemplate) => {
    setSelectedTemplate(template);
    setSessionConfig(prev => ({
      ...prev,
      template: template.id,
      title: `${template.name} Session`,
      description: template.description,
      goals: template.goals,
      duration: parseInt(template.duration.split('-')[0]) || 60
    }));
    setStep('configure');
  };

  const validateConfig = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!sessionConfig.title?.trim()) {
      newErrors.title = 'Session title is required';
    } else if (sessionConfig.title.length < 3) {
      newErrors.title = 'Session title must be at least 3 characters';
    }
    
    if (!sessionConfig.description?.trim()) {
      newErrors.description = 'Session description is required';
    }
    
    if (!sessionConfig.participants || sessionConfig.participants < 1) {
      newErrors.participants = 'Must have at least 1 participant';
    } else if (sessionConfig.participants > 20) {
      newErrors.participants = 'Maximum 20 participants allowed';
    }
    
    if (!sessionConfig.duration || sessionConfig.duration < 15) {
      newErrors.duration = 'Minimum duration is 15 minutes';
    } else if (sessionConfig.duration > 240) {
      newErrors.duration = 'Maximum duration is 240 minutes';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleConfigUpdate = (updates: Partial<SessionConfig>) => {
    setSessionConfig(prev => ({ ...prev, ...updates }));
    // Clear errors for updated fields
    if (updates.title !== undefined) {
      setErrors(prev => ({ ...prev, title: '' }));
    }
    if (updates.description !== undefined) {
      setErrors(prev => ({ ...prev, description: '' }));
    }
    if (updates.participants !== undefined) {
      setErrors(prev => ({ ...prev, participants: '' }));
    }
    if (updates.duration !== undefined) {
      setErrors(prev => ({ ...prev, duration: '' }));
    }
  };

  const handleCreateSession = async () => {
    if (!validateConfig()) {
      return;
    }
    
    setIsLoading(true);
    try {
      if (sessionConfig.title && sessionConfig.template) {
        await onSessionCreate(sessionConfig as SessionConfig);
      }
    } catch (error) {
      console.error('Failed to create session:', error);
      setErrors({ general: 'Failed to create session. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleNextToReview = () => {
    if (validateConfig()) {
      setStep('review');
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return theme.colors.success;
      case 'Intermediate': return theme.colors.warning;
      case 'Advanced': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  return (
    <div className={cn("max-w-4xl mx-auto", className)}>
      <AnimatePresence mode="wait">
        {/* Template Selection */}
        {step === 'template' && (
          <motion.div
            key="template"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
          >
            <div className="mb-6">
              <h2 className="text-2xl font-bold mb-2" style={{ color: theme.colors.text }}>
                Choose a Brainstorming Template
              </h2>
              <p style={{ color: theme.colors.textSecondary }}>
                Select a structured approach to guide your brainstorming session
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {BRAINSTORMING_TEMPLATES.map(template => (
                <motion.div
                  key={template.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Card 
                    className="cursor-pointer transition-all duration-200 hover:shadow-lg"
                    onClick={() => handleTemplateSelect(template)}
                    style={{ borderColor: template.color + '40' }}
                  >
                    <CardHeader>
                      <div className="flex items-center gap-3 mb-2">
                        <div 
                          className="p-2 rounded-lg"
                          style={{ backgroundColor: template.color + '20' }}
                        >
                          <template.icon 
                            className="w-6 h-6" 
                            style={{ color: template.color }}
                          />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{template.name}</CardTitle>
                          <Badge 
                            variant="secondary"
                            style={{ 
                              backgroundColor: getDifficultyColor(template.difficulty) + '20',
                              color: getDifficultyColor(template.difficulty)
                            }}
                          >
                            {template.difficulty}
                          </Badge>
                        </div>
                      </div>
                      <CardDescription>{template.description}</CardDescription>
                    </CardHeader>
                    
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4" style={{ color: theme.colors.textSecondary }} />
                          <span style={{ color: theme.colors.textSecondary }}>{template.duration}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Users className="w-4 h-4" style={{ color: theme.colors.textSecondary }} />
                          <span style={{ color: theme.colors.textSecondary }}>{template.participants}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Layers className="w-4 h-4" style={{ color: theme.colors.textSecondary }} />
                          <span style={{ color: theme.colors.textSecondary }}>{template.steps.length} steps</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            <div className="flex justify-between mt-6">
              <Button variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button disabled>
                Next
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </motion.div>
        )}

        {/* Configuration */}
        {step === 'configure' && selectedTemplate && (
          <motion.div
            key="configure"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
          >
            <div className="mb-6">
              <h2 className="text-2xl font-bold mb-2" style={{ color: theme.colors.text }}>
                Configure Your Session
              </h2>
              <p style={{ color: theme.colors.textSecondary }}>
                Customize the {selectedTemplate.name} session for your needs
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Session Title</Label>
                  <Input
                    id="title"
                    value={sessionConfig.title}
                    onChange={(e) => handleConfigUpdate({ title: e.target.value })}
                    placeholder="Enter session title"
                    className={errors.title ? 'border-destructive' : ''}
                  />
                  {errors.title && (
                    <div className="flex items-center gap-1 mt-1 text-sm text-destructive">
                      <AlertCircle className="h-3 w-3" />
                      {errors.title}
                    </div>
                  )}
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={sessionConfig.description}
                    onChange={(e) => handleConfigUpdate({ description: e.target.value })}
                    placeholder="Describe the session purpose"
                    rows={3}
                    className={errors.description ? 'border-destructive' : ''}
                  />
                  {errors.description && (
                    <div className="flex items-center gap-1 mt-1 text-sm text-destructive">
                      <AlertCircle className="h-3 w-3" />
                      {errors.description}
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="participants">Participants</Label>
                    <Input
                      id="participants"
                      type="number"
                      value={sessionConfig.participants}
                      onChange={(e) => handleConfigUpdate({ participants: parseInt(e.target.value) || 0 })}
                      min={1}
                      max={20}
                      className={errors.participants ? 'border-destructive' : ''}
                    />
                    {errors.participants && (
                      <div className="flex items-center gap-1 mt-1 text-sm text-destructive">
                        <AlertCircle className="h-3 w-3" />
                        {errors.participants}
                      </div>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="duration">Duration (minutes)</Label>
                    <Input
                      id="duration"
                      type="number"
                      value={sessionConfig.duration}
                      onChange={(e) => handleConfigUpdate({ duration: parseInt(e.target.value) || 0 })}
                      min={15}
                      max={240}
                      className={errors.duration ? 'border-destructive' : ''}
                    />
                    {errors.duration && (
                      <div className="flex items-center gap-1 mt-1 text-sm text-destructive">
                        <AlertCircle className="h-3 w-3" />
                        {errors.duration}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="tags">Tags (comma-separated)</Label>
                  <Input
                    id="tags"
                    value={sessionConfig.tags?.join(', ')}
                    onChange={(e) => handleConfigUpdate({ 
                      tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                    })}
                    placeholder="strategy, planning, innovation"
                  />
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-3" style={{ color: theme.colors.text }}>
                  Template Overview
                </h3>
                <Card>
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <selectedTemplate.icon 
                        className="w-6 h-6" 
                        style={{ color: selectedTemplate.color }}
                      />
                      <CardTitle>{selectedTemplate.name}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium mb-2">Steps:</h4>
                        <div className="space-y-1">
                          {selectedTemplate.steps.map((step, index) => (
                            <div key={step.id} className="flex items-center gap-2 text-sm">
                              <span 
                                className="w-5 h-5 rounded-full flex items-center justify-center text-xs font-medium"
                                style={{ 
                                  backgroundColor: selectedTemplate.color + '20',
                                  color: selectedTemplate.color
                                }}
                              >
                                {index + 1}
                              </span>
                              <span>{step.title}</span>
                              <span style={{ color: theme.colors.textSecondary }}>
                                ({step.duration})
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <Separator />

                      <div>
                        <h4 className="font-medium mb-2">Goals:</h4>
                        <div className="space-y-1">
                          {selectedTemplate.goals.map(goal => (
                            <div key={goal} className="flex items-center gap-2 text-sm">
                              <Target className="w-3 h-3" style={{ color: theme.colors.success }} />
                              <span>{goal}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="flex justify-between mt-6">
              <Button variant="outline" onClick={() => setStep('template')}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <Button 
                onClick={handleNextToReview}
                disabled={!sessionConfig.title || isLoading}
              >
                Review
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
            
            {errors.general && (
              <div className="flex items-center gap-2 mt-4 p-3 rounded-lg bg-destructive/10 border border-destructive/20">
                <AlertCircle className="h-4 w-4 text-destructive" />
                <span className="text-sm text-destructive">{errors.general}</span>
              </div>
            )}
          </motion.div>
        )}

        {/* Review */}
        {step === 'review' && selectedTemplate && (
          <motion.div
            key="review"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
          >
            <div className="mb-6">
              <h2 className="text-2xl font-bold mb-2" style={{ color: theme.colors.text }}>
                Review & Create Session
              </h2>
              <p style={{ color: theme.colors.textSecondary }}>
                Confirm your session details before creating
              </p>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <selectedTemplate.icon 
                    className="w-6 h-6" 
                    style={{ color: selectedTemplate.color }}
                  />
                  {sessionConfig.title}
                </CardTitle>
                <CardDescription>{sessionConfig.description}</CardDescription>
              </CardHeader>
              
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold" style={{ color: theme.colors.primary }}>
                      {sessionConfig.participants}
                    </div>
                    <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                      Participants
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold" style={{ color: theme.colors.primary }}>
                      {sessionConfig.duration}m
                    </div>
                    <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                      Duration
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold" style={{ color: theme.colors.primary }}>
                      {selectedTemplate.steps.length}
                    </div>
                    <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                      Steps
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold" style={{ color: theme.colors.primary }}>
                      {sessionConfig.goals?.length || 0}
                    </div>
                    <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                      Goals
                    </div>
                  </div>
                </div>

                {sessionConfig.tags && sessionConfig.tags.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-medium mb-2">Tags:</h4>
                    <div className="flex flex-wrap gap-2">
                      {sessionConfig.tags.map(tag => (
                        <Badge key={tag} variant="secondary">{tag}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="flex justify-between mt-6">
              <Button 
                variant="outline" 
                onClick={() => setStep('configure')}
                disabled={isLoading}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <Button 
                onClick={handleCreateSession}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Check className="w-4 h-4 mr-2" />
                )}
                {isLoading ? 'Creating...' : 'Create Session'}
              </Button>
            </div>
            
            {errors.general && (
              <div className="flex items-center gap-2 mt-4 p-3 rounded-lg bg-destructive/10 border border-destructive/20">
                <AlertCircle className="h-4 w-4 text-destructive" />
                <span className="text-sm text-destructive">{errors.general}</span>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
