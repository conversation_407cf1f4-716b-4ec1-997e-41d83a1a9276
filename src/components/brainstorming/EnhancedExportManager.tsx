/**
 * Enhanced Export Manager Component
 * 
 * Advanced export capabilities with comprehensive session data export
 * including PDF reports, CSV data, JSON backups, and visual exports.
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Download,
  FileText,
  Table,
  Code,
  Image,
  Share2,
  Settings,
  CheckCircle,
  AlertCircle,
  Loader2,
  Calendar,
  Tag,
  Filter,
  Eye,
  Clock,
  Users
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';
import { BrainstormSession, Idea } from '@/types/brainstorm';

interface ExportFormat {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  extension: string;
  features: string[];
  size: string;
  recommended?: boolean;
}

const EXPORT_FORMATS: ExportFormat[] = [
  {
    id: 'pdf',
    name: 'PDF Report',
    description: 'Professional report with ideas, analytics, and visualizations',
    icon: FileText,
    extension: 'pdf',
    features: ['Session overview', 'Ideas list', 'Analytics charts', 'Visual clusters', 'Participant summary'],
    size: '~2-5 MB',
    recommended: true
  },
  {
    id: 'csv',
    name: 'CSV Data',
    description: 'Structured data for spreadsheet analysis',
    icon: Table,
    extension: 'csv',
    features: ['Ideas data', 'Timestamps', 'Tags', 'Status tracking', 'Participant info'],
    size: '~50-200 KB'
  },
  {
    id: 'json',
    name: 'JSON Backup',
    description: 'Complete session data for backup or import',
    icon: Code,
    extension: 'json',
    features: ['Full session data', 'Relationships', 'Metadata', 'Import compatible'],
    size: '~100-500 KB'
  },
  {
    id: 'png',
    name: 'Idea Map Image',
    description: 'High-resolution visual of idea relationships',
    icon: Image,
    extension: 'png',
    features: ['Visual layout', 'High resolution', 'Cluster view', 'Print ready'],
    size: '~1-3 MB'
  }
];

interface ExportOptions {
  includeIdeas: boolean;
  includeMetrics: boolean;
  includeParticipants: boolean;
  includeTimestamps: boolean;
  includeAnalytics: boolean;
  customFileName: string;
  addWatermark: boolean;
  includePrivateNotes: boolean;
}

interface EnhancedExportManagerProps {
  session: BrainstormSession;
  ideas: Idea[];
  onExport: (format: string, options: ExportOptions) => Promise<void>;
  className?: string;
}

export const EnhancedExportManager: React.FC<EnhancedExportManagerProps> = ({
  session,
  ideas,
  onExport,
  className
}) => {
  const { theme } = useBrainstormingTheme();
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat | null>(EXPORT_FORMATS[0]);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    includeIdeas: true,
    includeMetrics: true,
    includeParticipants: false,
    includeTimestamps: true,
    includeAnalytics: true,
    customFileName: `${session.title}_export`,
    addWatermark: false,
    includePrivateNotes: false
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportStatus, setExportStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [previewMode, setPreviewMode] = useState(false);

  const handleExport = async () => {
    if (!selectedFormat) return;

    setIsExporting(true);
    setExportStatus('idle');
    setExportProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      await onExport(selectedFormat.id, exportOptions);
      
      clearInterval(progressInterval);
      setExportProgress(100);
      setExportStatus('success');
      
      setTimeout(() => {
        setExportStatus('idle');
        setExportProgress(0);
      }, 3000);
    } catch (error) {
      setExportStatus('error');
      setTimeout(() => setExportStatus('idle'), 3000);
    } finally {
      setIsExporting(false);
    }
  };

  const updateExportOption = (key: keyof ExportOptions, value: any) => {
    setExportOptions(prev => ({ ...prev, [key]: value }));
  };

  const getSessionStats = () => ({
    totalIdeas: ideas.length,
    totalParticipants: session.participants?.length || 1,
    sessionDuration: session.updatedAt ? 
      Math.round((new Date(session.updatedAt).getTime() - new Date(session.createdAt).getTime()) / (1000 * 60)) : 0,
    lastActivity: session.updatedAt ? new Date(session.updatedAt).toLocaleDateString() : 'Unknown'
  });

  const stats = getSessionStats();

  return (
    <div className={cn("space-y-6", className)}>
      {/* Session Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="w-5 h-5" style={{ color: theme.colors.primary }} />
            Export Session: {session.title}
          </CardTitle>
          <CardDescription>
            Export your brainstorming session data in various formats
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 rounded-lg" style={{ backgroundColor: theme.colors.primary + '10' }}>
              <div className="text-2xl font-bold" style={{ color: theme.colors.primary }}>
                {stats.totalIdeas}
              </div>
              <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                Ideas
              </div>
            </div>
            
            <div className="text-center p-3 rounded-lg" style={{ backgroundColor: theme.colors.accent + '10' }}>
              <div className="text-2xl font-bold" style={{ color: theme.colors.accent }}>
                {stats.totalParticipants}
              </div>
              <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                Participants
              </div>
            </div>
            
            <div className="text-center p-3 rounded-lg" style={{ backgroundColor: theme.colors.success + '10' }}>
              <div className="text-2xl font-bold" style={{ color: theme.colors.success }}>
                {stats.sessionDuration}m
              </div>
              <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                Duration
              </div>
            </div>
            
            <div className="text-center p-3 rounded-lg" style={{ backgroundColor: theme.colors.warning + '10' }}>
              <div className="text-2xl font-bold" style={{ color: theme.colors.warning }}>
                {stats.lastActivity}
              </div>
              <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                Last Activity
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Format Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Choose Export Format</CardTitle>
          <CardDescription>
            Select the format that best suits your needs
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {EXPORT_FORMATS.map(format => (
              <motion.div
                key={format.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card 
                  className={cn(
                    "cursor-pointer transition-all duration-200 hover:shadow-md",
                    selectedFormat?.id === format.id && "ring-2 ring-offset-2"
                  )}
                  style={{
                    ringColor: selectedFormat?.id === format.id ? theme.colors.primary : 'transparent',
                    backgroundColor: selectedFormat?.id === format.id ? theme.colors.primary + '10' : 'transparent'
                  }}
                  onClick={() => setSelectedFormat(format)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div 
                        className="p-2 rounded-lg"
                        style={{ backgroundColor: theme.colors.primary + '20' }}
                      >
                        <format.icon 
                          className="w-5 h-5" 
                          style={{ color: theme.colors.primary }}
                        />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="font-medium" style={{ color: theme.colors.text }}>
                            {format.name}
                          </h3>
                          <div className="flex gap-1">
                            {format.recommended && (
                              <Badge variant="default" className="text-xs">
                                Recommended
                              </Badge>
                            )}
                            <Badge variant="outline" className="text-xs">
                              .{format.extension}
                            </Badge>
                          </div>
                        </div>
                        <p className="text-sm mb-2" style={{ color: theme.colors.textSecondary }}>
                          {format.description}
                        </p>
                        <div className="flex flex-wrap gap-1 mb-2">
                          {format.features.slice(0, 3).map(feature => (
                            <Badge key={feature} variant="secondary" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                          {format.features.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{format.features.length - 3}
                            </Badge>
                          )}
                        </div>
                        <div className="text-xs" style={{ color: theme.colors.textSecondary }}>
                          Size: {format.size}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Export Options */}
      {selectedFormat && (
        <Card>
          <CardHeader>
            <CardTitle>Export Options</CardTitle>
            <CardDescription>
              Customize what to include in your {selectedFormat.name} export
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="fileName">File Name</Label>
              <Input
                id="fileName"
                value={exportOptions.customFileName}
                onChange={(e) => updateExportOption('customFileName', e.target.value)}
                placeholder="Enter custom file name"
              />
            </div>

            <Separator />

            <div className="space-y-3">
              <h4 className="font-medium" style={{ color: theme.colors.text }}>
                Include in Export
              </h4>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeIdeas"
                    checked={exportOptions.includeIdeas}
                    onCheckedChange={(checked) => updateExportOption('includeIdeas', checked)}
                  />
                  <Label htmlFor="includeIdeas">Ideas & Content</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeMetrics"
                    checked={exportOptions.includeMetrics}
                    onCheckedChange={(checked) => updateExportOption('includeMetrics', checked)}
                  />
                  <Label htmlFor="includeMetrics">Session Metrics</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeParticipants"
                    checked={exportOptions.includeParticipants}
                    onCheckedChange={(checked) => updateExportOption('includeParticipants', checked)}
                  />
                  <Label htmlFor="includeParticipants">Participant Info</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeTimestamps"
                    checked={exportOptions.includeTimestamps}
                    onCheckedChange={(checked) => updateExportOption('includeTimestamps', checked)}
                  />
                  <Label htmlFor="includeTimestamps">Timestamps</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeAnalytics"
                    checked={exportOptions.includeAnalytics}
                    onCheckedChange={(checked) => updateExportOption('includeAnalytics', checked)}
                  />
                  <Label htmlFor="includeAnalytics">Analytics Charts</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="addWatermark"
                    checked={exportOptions.addWatermark}
                    onCheckedChange={(checked) => updateExportOption('addWatermark', checked)}
                  />
                  <Label htmlFor="addWatermark">Add Watermark</Label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Export Actions */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => setPreviewMode(!previewMode)}
                disabled={!selectedFormat}
              >
                <Eye className="w-4 h-4 mr-2" />
                Preview
              </Button>
              
              <Button
                variant="outline"
                disabled={!selectedFormat}
              >
                <Share2 className="w-4 h-4 mr-2" />
                Share Link
              </Button>
            </div>
            
            <div className="flex items-center gap-2">
              {exportStatus === 'success' && (
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm">Export completed!</span>
                </div>
              )}
              
              {exportStatus === 'error' && (
                <div className="flex items-center gap-2 text-red-600">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-sm">Export failed</span>
                </div>
              )}
              
              <Button
                onClick={handleExport}
                disabled={!selectedFormat || isExporting}
                style={{ backgroundColor: theme.colors.primary }}
              >
                {isExporting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4 mr-2" />
                    Export {selectedFormat?.name}
                  </>
                )}
              </Button>
            </div>
          </div>
          
          {isExporting && (
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-1">
                <span>Exporting...</span>
                <span>{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} className="h-2" />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
