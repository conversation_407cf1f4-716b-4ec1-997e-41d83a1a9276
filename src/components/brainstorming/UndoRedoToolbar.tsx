/**
 * Undo/Redo Toolbar Component
 * 
 * Provides visual controls for undo/redo operations with keyboard shortcut indicators
 * and action history tooltips.
 */

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  Undo2,
  Redo2,
  History,
  Trash2,
  Clock,
  Plus,
  Edit3,
  MoreHorizontal
} from 'lucide-react';
import { useUndoRedo } from '@/hooks/useUndoRedo';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';
import { cn } from '@/lib/utils';

interface UndoRedoToolbarProps {
  className?: string;
  showHistory?: boolean;
  compact?: boolean;
}

export const UndoRedoToolbar: React.FC<UndoRedoToolbarProps> = ({
  className,
  showHistory = true,
  compact = false
}) => {
  const { theme } = useBrainstormingTheme();
  const {
    canUndo,
    canRedo,
    undo,
    redo,
    clearHistory,
    undoStackSize,
    redoStackSize,
    recentActions
  } = useUndoRedo();

  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'create': return <Plus className="h-3 w-3" />;
      case 'update': return <Edit3 className="h-3 w-3" />;
      case 'delete': return <Trash2 className="h-3 w-3" />;
      case 'bulk_update': return <Edit3 className="h-3 w-3" />;
      case 'bulk_delete': return <Trash2 className="h-3 w-3" />;
      default: return <History className="h-3 w-3" />;
    }
  };

  const getActionLabel = (actionType: string) => {
    switch (actionType) {
      case 'create': return 'Created idea';
      case 'update': return 'Updated idea';
      case 'delete': return 'Deleted idea';
      case 'bulk_update': return 'Updated multiple ideas';
      case 'bulk_delete': return 'Deleted multiple ideas';
      default: return 'Unknown action';
    }
  };

  const formatTime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return `${Math.floor(diff / 86400000)}d ago`;
  };

  if (compact) {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={undo}
                disabled={!canUndo}
                className="h-8 w-8 p-0"
              >
                <Undo2 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Undo (Ctrl+Z)</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={redo}
                disabled={!canRedo}
                className="h-8 w-8 p-0"
              >
                <Redo2 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Redo (Ctrl+Shift+Z)</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    );
  }

  return (
    <motion.div
      className={cn(
        "flex items-center gap-2 p-2 rounded-lg border backdrop-blur-sm",
        className
      )}
      style={{
        backgroundColor: theme.colors.surface + 'CC',
        borderColor: theme.colors.border
      }}
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
    >
      {/* Undo Button */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={canUndo ? "ghost" : "ghost"}
              size="sm"
              onClick={undo}
              disabled={!canUndo}
              className={cn(
                "relative",
                canUndo && "hover:bg-primary/10"
              )}
              style={{ color: canUndo ? theme.colors.text : theme.colors.textSecondary }}
            >
              <Undo2 className="h-4 w-4 mr-1" />
              Undo
              {undoStackSize > 0 && (
                <Badge 
                  variant="secondary" 
                  className="ml-1 h-4 px-1 text-xs"
                  style={{
                    backgroundColor: theme.colors.primary + '20',
                    color: theme.colors.primary
                  }}
                >
                  {undoStackSize}
                </Badge>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <p>Undo last action</p>
              <p className="text-xs text-muted-foreground">Ctrl+Z</p>
              {recentActions.length > 0 && (
                <p className="text-xs text-muted-foreground mt-1">
                  Last: {getActionLabel(recentActions[0].type)}
                </p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* Redo Button */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={canRedo ? "ghost" : "ghost"}
              size="sm"
              onClick={redo}
              disabled={!canRedo}
              className={cn(
                "relative",
                canRedo && "hover:bg-primary/10"
              )}
              style={{ color: canRedo ? theme.colors.text : theme.colors.textSecondary }}
            >
              <Redo2 className="h-4 w-4 mr-1" />
              Redo
              {redoStackSize > 0 && (
                <Badge 
                  variant="secondary" 
                  className="ml-1 h-4 px-1 text-xs"
                  style={{
                    backgroundColor: theme.colors.accent + '20',
                    color: theme.colors.accent
                  }}
                >
                  {redoStackSize}
                </Badge>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <p>Redo last undone action</p>
              <p className="text-xs text-muted-foreground">Ctrl+Shift+Z</p>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* History Dropdown */}
      {showHistory && (undoStackSize > 0 || redoStackSize > 0) && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="relative"
              style={{ color: theme.colors.textSecondary }}
            >
              <History className="h-4 w-4 mr-1" />
              History
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent 
            align="end" 
            className="w-64"
            style={{
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.border
            }}
          >
            {/* Recent Actions */}
            {recentActions.length > 0 && (
              <>
                <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground">
                  Recent Actions
                </div>
                {recentActions.map((action, index) => (
                  <DropdownMenuItem 
                    key={action.id}
                    className="flex items-center justify-between"
                    style={{ color: theme.colors.text }}
                  >
                    <div className="flex items-center gap-2">
                      {getActionIcon(action.type)}
                      <span className="text-sm">{getActionLabel(action.type)}</span>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {formatTime(action.timestamp)}
                    </div>
                  </DropdownMenuItem>
                ))}
                <DropdownMenuSeparator />
              </>
            )}

            {/* Clear History */}
            <DropdownMenuItem 
              onClick={clearHistory}
              className="text-destructive hover:text-destructive-foreground hover:bg-destructive/10"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear History
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* Keyboard Shortcuts Indicator */}
      <div className="hidden md:flex items-center gap-1 text-xs text-muted-foreground pl-2 border-l" style={{ borderColor: theme.colors.border }}>
        <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Ctrl</kbd>
        <span>+</span>
        <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Z</kbd>
        <span className="mx-1">/</span>
        <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Y</kbd>
      </div>
    </motion.div>
  );
};