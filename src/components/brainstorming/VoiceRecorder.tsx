import React, { useState, useEffect, useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Mic,
  MicOff,
  Square,
  Play,
  Pause,
  Settings,
  FileAudio,
  MessageSquare,
  Lightbulb,
  CheckSquare,
  Heart,
  Loader2,
  Volume2,
  VolumeX,
  Waveform,
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { 
  voiceIntegration, 
  VoiceConfig, 
  TranscriptionResult, 
  VoiceNote,
  getVoiceNotes,
} from '@/lib/voice-integration-enhanced';

interface VoiceRecorderProps {
  sessionId: string;
  onIdeaExtracted?: (idea: string) => void;
  onActionItemCreated?: (actionItem: string) => void;
}

export const VoiceRecorder: React.FC<VoiceRecorderProps> = ({
  sessionId,
  onIdeaExtracted,
  onActionItemCreated,
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentTranscription, setCurrentTranscription] = useState<TranscriptionResult | null>(null);
  const [partialText, setPartialText] = useState('');
  const [voiceNotes, setVoiceNotes] = useState<VoiceNote[]>([]);
  const [audioLevel, setAudioLevel] = useState(0);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [config, setConfig] = useState<VoiceConfig>({
    provider: 'whisper',
    language: 'en-US',
    quality: 'medium',
    realTimeProcessing: false,
    voiceCommands: true,
    speakerDiarization: false,
    punctuation: true,
    profanityFilter: false,
  });
  
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const recordingTimerRef = useRef<number | null>(null);
  
  const { toast } = useToast();
  const { addIdea, addMessage } = useBrainstormStore();

  useEffect(() => {
    // Set up voice integration event listeners
    voiceIntegration.on('recordingStarted', handleRecordingStarted);
    voiceIntegration.on('partialTranscription', handlePartialTranscription);
    voiceIntegration.on('transcriptionComplete', handleTranscriptionComplete);
    voiceIntegration.on('voiceCommands', handleVoiceCommands);
    voiceIntegration.on('processingStarted', () => setIsProcessing(true));
    voiceIntegration.on('processingComplete', () => setIsProcessing(false));

    loadVoiceNotes();

    return () => {
      voiceIntegration.removeAllListeners();
      cleanup();
    };
  }, []);

  const loadVoiceNotes = async () => {
    try {
      const notes = await getVoiceNotes(sessionId);
      setVoiceNotes(notes);
    } catch (error) {
      console.error('Failed to load voice notes:', error);
    }
  };

  const handleRecordingStarted = () => {
    setIsRecording(true);
    startAudioLevelMonitoring();
    startRecordingTimer();
  };

  const handlePartialTranscription = (result: Partial<TranscriptionResult>) => {
    if (result.text) {
      setPartialText(result.text);
    }
  };

  const handleTranscriptionComplete = async (result: TranscriptionResult) => {
    setCurrentTranscription(result);
    setPartialText('');
    
    // Create voice note with AI analysis
    try {
      const voiceNote = await voiceIntegration.createVoiceNote(result);
      setVoiceNotes(prev => [voiceNote, ...prev]);
      
      // Extract ideas and add them to the session
      voiceNote.extractedIdeas.forEach(ideaText => {
        if (onIdeaExtracted) {
          onIdeaExtracted(ideaText);
        } else {
          addIdea(sessionId, ideaText, []);
        }
      });
      
      // Add action items
      voiceNote.actionItems.forEach(actionItem => {
        if (onActionItemCreated) {
          onActionItemCreated(actionItem);
        }
      });
      
      // Add transcription as a message
      addMessage(sessionId, {
        type: 'user',
        content: `🎤 Voice Note: ${result.text}`,
        timestamp: new Date().toISOString(),
        metadata: {
          transcriptionId: result.id,
          confidence: result.confidence,
          duration: result.duration,
        },
      });

      toast({
        message: `Voice note processed: ${voiceNote.extractedIdeas.length} ideas extracted`,
        type: 'success',
      });
    } catch (error) {
      toast({
        message: 'Failed to process voice note',
        type: 'error',
      });
    }
  };

  const handleVoiceCommands = (commands: any[]) => {
    commands.forEach(command => {
      executeVoiceCommand(command);
    });
  };

  const executeVoiceCommand = (command: any) => {
    switch (command.action) {
      case 'create_idea':
        if (command.parameters?.text) {
          addIdea(sessionId, command.parameters.text, []);
          toast({
            message: 'Idea created via voice command',
            type: 'success',
          });
        }
        break;
      case 'save_session':
        // Trigger session save
        toast({
          message: 'Session saved via voice command',
          type: 'success',
        });
        break;
      case 'start_recording':
        if (!isRecording) {
          startRecording();
        }
        break;
      case 'stop_recording':
        if (isRecording) {
          stopRecording();
        }
        break;
      default:
        console.log('Unknown voice command:', command);
    }
  };

  const startRecording = async () => {
    try {
      voiceIntegration.updateConfig(config);
      await voiceIntegration.startRecording();
    } catch (error) {
      toast({
        message: 'Failed to start recording',
        type: 'error',
      });
    }
  };

  const stopRecording = async () => {
    try {
      await voiceIntegration.stopRecording();
      setIsRecording(false);
      cleanup();
    } catch (error) {
      toast({
        message: 'Failed to stop recording',
        type: 'error',
      });
    }
  };

  const startAudioLevelMonitoring = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      
      const source = audioContextRef.current.createMediaStreamSource(stream);
      source.connect(analyserRef.current);
      
      analyserRef.current.fftSize = 256;
      const bufferLength = analyserRef.current.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      
      const updateAudioLevel = () => {
        if (analyserRef.current && isRecording) {
          analyserRef.current.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;
          setAudioLevel((average / 255) * 100);
          requestAnimationFrame(updateAudioLevel);
        }
      };
      
      updateAudioLevel();
    } catch (error) {
      console.error('Failed to set up audio monitoring:', error);
    }
  };

  const startRecordingTimer = () => {
    recordingTimerRef.current = setInterval(() => {
      setRecordingDuration(prev => prev + 1);
    }, 1000);
  };

  const cleanup = () => {
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }
    setAudioLevel(0);
    setRecordingDuration(0);
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return <Heart className="h-4 w-4 text-green-600" />;
      case 'negative':
        return <Heart className="h-4 w-4 text-red-600" />;
      default:
        return <Heart className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <Card className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Mic className="h-5 w-5" />
          Voice Recorder
        </h3>
        {config.voiceCommands && (
          <Badge variant="outline">Voice Commands Enabled</Badge>
        )}
      </div>

      <Tabs defaultValue="recorder" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="recorder">Record</TabsTrigger>
          <TabsTrigger value="notes">Notes ({voiceNotes.length})</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="recorder" className="space-y-4">
          {/* Recording Controls */}
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <Button
                size="lg"
                variant={isRecording ? "destructive" : "default"}
                onClick={isRecording ? stopRecording : startRecording}
                disabled={isProcessing}
                className="rounded-full w-16 h-16"
              >
                {isProcessing ? (
                  <Loader2 className="h-8 w-8 animate-spin" />
                ) : isRecording ? (
                  <Square className="h-8 w-8" />
                ) : (
                  <Mic className="h-8 w-8" />
                )}
              </Button>
              
              {isRecording && (
                <div className="absolute -inset-2 rounded-full border-2 border-red-500 animate-pulse" />
              )}
            </div>

            {isRecording && (
              <div className="text-center space-y-2">
                <div className="text-lg font-mono">
                  {formatDuration(recordingDuration)}
                </div>
                <div className="w-32 space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span>Audio Level</span>
                    <span>{Math.round(audioLevel)}%</span>
                  </div>
                  <Progress value={audioLevel} className="h-2" />
                </div>
              </div>
            )}

            {isProcessing && (
              <div className="text-center">
                <p className="text-sm text-muted-foreground">
                  Processing audio...
                </p>
              </div>
            )}
          </div>

          {/* Real-time Transcription */}
          {config.realTimeProcessing && partialText && (
            <Card className="p-3">
              <h4 className="text-sm font-medium mb-2">Live Transcription</h4>
              <p className="text-sm text-muted-foreground italic">
                {partialText}...
              </p>
            </Card>
          )}

          {/* Latest Transcription */}
          {currentTranscription && (
            <Card className="p-3">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium">Latest Transcription</h4>
                <Badge variant="outline">
                  {Math.round(currentTranscription.confidence * 100)}% confidence
                </Badge>
              </div>
              <p className="text-sm">{currentTranscription.text}</p>
              <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                <span>Duration: {currentTranscription.duration.toFixed(1)}s</span>
                <span>Language: {currentTranscription.language}</span>
                <span>Provider: {currentTranscription.provider}</span>
              </div>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="notes" className="space-y-4">
          <ScrollArea className="h-64">
            <div className="space-y-3">
              {voiceNotes.map(note => (
                <Card key={note.id} className="p-3">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <FileAudio className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        {new Date(note.createdAt).toLocaleTimeString()}
                      </span>
                    </div>
                    {getSentimentIcon(note.sentiment)}
                  </div>
                  
                  <p className="text-sm mb-2">{note.summary}</p>
                  
                  {note.extractedIdeas.length > 0 && (
                    <div className="space-y-1">
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Lightbulb className="h-3 w-3" />
                        <span>Ideas ({note.extractedIdeas.length})</span>
                      </div>
                      {note.extractedIdeas.slice(0, 2).map((idea, idx) => (
                        <p key={idx} className="text-xs pl-4 border-l-2 border-yellow-200">
                          {idea}
                        </p>
                      ))}
                    </div>
                  )}
                  
                  {note.actionItems.length > 0 && (
                    <div className="space-y-1 mt-2">
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <CheckSquare className="h-3 w-3" />
                        <span>Action Items ({note.actionItems.length})</span>
                      </div>
                      {note.actionItems.slice(0, 2).map((item, idx) => (
                        <p key={idx} className="text-xs pl-4 border-l-2 border-blue-200">
                          {item}
                        </p>
                      ))}
                    </div>
                  )}
                </Card>
              ))}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <div className="space-y-4">
            <div>
              <Label htmlFor="provider">Speech Provider</Label>
              <Select
                value={config.provider}
                onValueChange={(value) => setConfig(prev => ({ ...prev, provider: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="whisper">Whisper (Local)</SelectItem>
                  <SelectItem value="azure">Azure Speech</SelectItem>
                  <SelectItem value="google">Google Speech</SelectItem>
                  <SelectItem value="aws">AWS Transcribe</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="quality">Quality</Label>
              <Select
                value={config.quality}
                onValueChange={(value) => setConfig(prev => ({ ...prev, quality: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low (Faster)</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High (Slower)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="realtime">Real-time Processing</Label>
                <Switch
                  id="realtime"
                  checked={config.realTimeProcessing}
                  onCheckedChange={(checked) => 
                    setConfig(prev => ({ ...prev, realTimeProcessing: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="commands">Voice Commands</Label>
                <Switch
                  id="commands"
                  checked={config.voiceCommands}
                  onCheckedChange={(checked) => 
                    setConfig(prev => ({ ...prev, voiceCommands: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="diarization">Speaker Separation</Label>
                <Switch
                  id="diarization"
                  checked={config.speakerDiarization}
                  onCheckedChange={(checked) => 
                    setConfig(prev => ({ ...prev, speakerDiarization: checked }))
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="punctuation">Auto Punctuation</Label>
                <Switch
                  id="punctuation"
                  checked={config.punctuation}
                  onCheckedChange={(checked) => 
                    setConfig(prev => ({ ...prev, punctuation: checked }))
                  }
                />
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </Card>
  );
};