/**
 * Session Continuation Banner Component
 * 
 * Shows when a brainstorming session can be resumed with previous context
 */

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Play,
  Pause,
  X,
  Clock,
  MessageSquare,
  ArrowRight,
  RefreshCw
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import { cn } from '@/lib/utils';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';

interface SessionContinuationBannerProps {
  sessionTitle: string;
  lastActivity: string;
  continuationPrompt?: string;
  messageCount: number;
  onResume: () => void;
  onDismiss: () => void;
  onStartFresh: () => void;
  className?: string;
}

export const SessionContinuationBanner: React.FC<SessionContinuationBannerProps> = ({
  sessionTitle,
  lastActivity,
  continuationPrompt,
  messageCount,
  onResume,
  onDismiss,
  onStartFresh,
  className
}) => {
  const { theme } = useBrainstormingTheme();

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const then = new Date(timestamp);
    const diffMs = now.getTime() - then.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -20, scale: 0.95 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className={cn("relative", className)}
      >
        <Card className="border-l-4 shadow-lg" style={{ borderLeftColor: theme.colors.primary }}>
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-2 h-2 rounded-full animate-pulse"
                      style={{ backgroundColor: theme.colors.success }}
                    />
                    <h3 className="font-medium" style={{ color: theme.colors.text }}>
                      Resume Session: {sessionTitle}
                    </h3>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    Paused
                  </Badge>
                </div>

                <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span>Last active {formatTimeAgo(lastActivity)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MessageSquare className="w-3 h-3" />
                    <span>{messageCount} messages</span>
                  </div>
                </div>

                {continuationPrompt && (
                  <div className="mb-3">
                    <p className="text-sm text-muted-foreground mb-1">
                      You were working on:
                    </p>
                    <div 
                      className="p-2 rounded-md text-sm border-l-2"
                      style={{ 
                        backgroundColor: theme.colors.accent + '10',
                        borderLeftColor: theme.colors.accent
                      }}
                    >
                      <span className="italic">"{continuationPrompt}"</span>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  <Button
                    onClick={onResume}
                    size="sm"
                    className="flex items-center gap-2"
                    style={{ backgroundColor: theme.colors.primary }}
                  >
                    <Play className="w-4 h-4" />
                    Resume Session
                    <ArrowRight className="w-3 h-3" />
                  </Button>

                  <Button
                    onClick={onStartFresh}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className="w-4 h-4" />
                    Start Fresh
                  </Button>
                </div>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Animated background gradient */}
        <div 
          className="absolute inset-0 rounded-lg opacity-5 pointer-events-none"
          style={{
            background: `linear-gradient(45deg, ${theme.colors.primary}, ${theme.colors.accent})`
          }}
        />
      </motion.div>
    </AnimatePresence>
  );
};

// Hook for managing session continuation state
export function useSessionContinuation(sessionId: string) {
  const [showBanner, setShowBanner] = React.useState(false);
  const [bannerData, setBannerData] = React.useState<{
    sessionTitle: string;
    lastActivity: string;
    continuationPrompt?: string;
    messageCount: number;
  } | null>(null);

  const checkForContinuation = React.useCallback((sessionData: {
    title: string;
    lastActivity: string;
    continuationPrompt?: string;
    messageCount: number;
    shouldAutoResume: boolean;
  }) => {
    if (sessionData.shouldAutoResume) {
      setBannerData({
        sessionTitle: sessionData.title,
        lastActivity: sessionData.lastActivity,
        continuationPrompt: sessionData.continuationPrompt,
        messageCount: sessionData.messageCount
      });
      setShowBanner(true);
    }
  }, []);

  const dismissBanner = React.useCallback(() => {
    setShowBanner(false);
    setBannerData(null);
  }, []);

  const resumeSession = React.useCallback(() => {
    setShowBanner(false);
    // Resume logic would be handled by parent component
  }, []);

  const startFresh = React.useCallback(() => {
    setShowBanner(false);
    setBannerData(null);
    // Start fresh logic would be handled by parent component
  }, []);

  return {
    showBanner,
    bannerData,
    checkForContinuation,
    dismissBanner,
    resumeSession,
    startFresh
  };
}
