/**
 * Session Switcher Component
 * 
 * Compact interface for quickly switching between brainstorming sessions.
 * Provides dropdown-style session selection with recent sessions and quick actions.
 */

import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronDown,
  Brain,
  Plus,
  Clock,
  Lightbulb,
  MessageSquare,
  Calendar,
  Star,
  Search,
  ArrowRight,
  Dot,
  CheckCircle
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import { cn } from '@/lib/utils';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { BrainstormSession, TemplateType } from '@/types/brainstorm';
import { flowEngine } from '@/lib/brainstorming/flowEngine';

interface SessionSwitcherProps {
  currentSessionId?: string;
  onSessionSelect: (session: BrainstormSession) => void;
  onCreateNew?: () => void;
  className?: string;
  variant?: 'compact' | 'full' | 'minimal';
  showCreateButton?: boolean;
  maxRecentSessions?: number;
}

interface SessionGroup {
  label: string;
  sessions: BrainstormSession[];
}

const TEMPLATE_ICONS: Record<TemplateType, string> = {
  [TemplateType.SWOT]: '📊',
  [TemplateType.FIVE_WHYS]: '❓',
  [TemplateType.DESIGN_THINKING]: '🎨',
  [TemplateType.CANVAS]: '🗺️',
  [TemplateType.CUSTOM]: '✨'
};

export const SessionSwitcher: React.FC<SessionSwitcherProps> = ({
  currentSessionId,
  onSessionSelect,
  onCreateNew,
  className,
  variant = 'compact',
  showCreateButton = true,
  maxRecentSessions = 10
}) => {
  const {
    sessions,
    currentSessionId: storeSessionId,
    getIdeasBySession,
    setCurrentSession
  } = useBrainstormStore();

  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const effectiveCurrentSessionId = currentSessionId || storeSessionId;
  const currentSession = effectiveCurrentSessionId ? sessions[effectiveCurrentSessionId] : null;

  // Group sessions by recency and activity
  const sessionGroups = useMemo((): SessionGroup[] => {
    const allSessions = Object.values(sessions);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    const groups: SessionGroup[] = [];

    // Filter sessions based on search query
    const filteredSessions = searchQuery
      ? allSessions.filter(session =>
          session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          session.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
        )
      : allSessions;

    if (!searchQuery) {
      // Today's sessions
      const todaySessions = filteredSessions
        .filter(session => new Date(session.updatedAt) >= today)
        .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        .slice(0, maxRecentSessions);

      if (todaySessions.length > 0) {
        groups.push({ label: 'Today', sessions: todaySessions });
      }

      // Yesterday's sessions
      const yesterdaySessions = filteredSessions
        .filter(session => {
          const updatedAt = new Date(session.updatedAt);
          return updatedAt >= yesterday && updatedAt < today;
        })
        .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        .slice(0, 5);

      if (yesterdaySessions.length > 0) {
        groups.push({ label: 'Yesterday', sessions: yesterdaySessions });
      }

      // This week's sessions
      const thisWeekSessions = filteredSessions
        .filter(session => {
          const updatedAt = new Date(session.updatedAt);
          return updatedAt >= weekAgo && updatedAt < yesterday;
        })
        .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        .slice(0, 5);

      if (thisWeekSessions.length > 0) {
        groups.push({ label: 'This Week', sessions: thisWeekSessions });
      }

      // Older sessions
      const olderSessions = filteredSessions
        .filter(session => new Date(session.updatedAt) < weekAgo)
        .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        .slice(0, 8);

      if (olderSessions.length > 0) {
        groups.push({ label: 'Older', sessions: olderSessions });
      }
    } else {
      // When searching, show all results in one group
      const searchResults = filteredSessions
        .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        .slice(0, 20);

      if (searchResults.length > 0) {
        groups.push({ label: 'Search Results', sessions: searchResults });
      }
    }

    return groups;
  }, [sessions, searchQuery, maxRecentSessions]);

  const handleSessionSelect = useCallback((session: BrainstormSession) => {
    setCurrentSession(session.id);
    onSessionSelect(session);
    setOpen(false);
  }, [setCurrentSession, onSessionSelect]);

  const renderSessionItem = (session: BrainstormSession, isCompact = false) => {
    const isActive = session.id === effectiveCurrentSessionId;
    const ideas = getIdeasBySession(session.id);
    const flowProgress = flowEngine.getFlowProgress(session.id);
    
    const sessionAge = new Date().getTime() - new Date(session.updatedAt).getTime();
    const isRecent = sessionAge < 24 * 60 * 60 * 1000; // Less than 24 hours

    return (
      <div
        className={cn(
          "flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-all duration-200 hover:bg-accent",
          isActive && "bg-accent border border-primary/20",
          isCompact && "p-1.5"
        )}
        onClick={() => handleSessionSelect(session)}
      >
        <div className="flex items-center gap-2">
          {session.template && (
            <span className={cn("text-sm", isCompact && "text-xs")}>
              {TEMPLATE_ICONS[session.template]}
            </span>
          )}
          <Brain className={cn(
            "h-4 w-4",
            isActive ? "text-primary" : "text-muted-foreground",
            isCompact && "h-3 w-3"
          )} />
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className={cn(
              "font-medium truncate",
              isCompact ? "text-xs" : "text-sm",
              isActive && "text-primary"
            )}>
              {session.title}
            </span>
            
            {isRecent && (
              <div className="h-1.5 w-1.5 bg-green-500 rounded-full" />
            )}
            
            {flowProgress && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="h-1.5 w-1.5 bg-blue-500 rounded-full animate-pulse" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Active flow</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
          
          {!isCompact && (
            <div className="flex items-center gap-3 mt-1 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <MessageSquare className="h-3 w-3" />
                <span>{session.messages.length}</span>
              </div>
              
              <div className="flex items-center gap-1">
                <Lightbulb className="h-3 w-3" />
                <span>{ideas.length}</span>
              </div>
              
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{new Date(session.updatedAt).toLocaleDateString()}</span>
              </div>
            </div>
          )}
        </div>

        {isActive && (
          <CheckCircle className="h-3 w-3 text-primary" />
        )}
      </div>
    );
  };

  const renderCurrentSessionDisplay = () => {
    if (!currentSession) {
      return (
        <div className="flex items-center gap-2 text-muted-foreground">
          <Brain className="h-4 w-4" />
          <span className="text-sm">No session selected</span>
        </div>
      );
    }

    const ideas = getIdeasBySession(currentSession.id);
    const flowProgress = flowEngine.getFlowProgress(currentSession.id);

    if (variant === 'minimal') {
      return (
        <div className="flex items-center gap-2">
          {currentSession.template && (
            <span className="text-sm">{TEMPLATE_ICONS[currentSession.template]}</span>
          )}
          <span className="text-sm font-medium truncate max-w-32">
            {currentSession.title}
          </span>
          {flowProgress && (
            <div className="h-1.5 w-1.5 bg-blue-500 rounded-full animate-pulse" />
          )}
        </div>
      );
    }

    return (
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          {currentSession.template && (
            <span className="text-lg">{TEMPLATE_ICONS[currentSession.template]}</span>
          )}
          <Brain className="h-4 w-4 text-primary" />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium truncate">
              {currentSession.title}
            </span>
            {flowProgress && (
              <div className="h-1.5 w-1.5 bg-blue-500 rounded-full animate-pulse" />
            )}
          </div>
          
          {variant === 'full' && (
            <div className="flex items-center gap-3 mt-1 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <MessageSquare className="h-3 w-3" />
                <span>{currentSession.messages.length} messages</span>
              </div>
              
              <div className="flex items-center gap-1">
                <Lightbulb className="h-3 w-3" />
                <span>{ideas.length} ideas</span>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  if (variant === 'minimal') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className={cn("justify-start", className)}>
            {renderCurrentSessionDisplay()}
            <ChevronDown className="h-3 w-3 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-80">
          <DropdownMenuLabel>Switch Session</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {sessionGroups.slice(0, 2).map(group => (
            <React.Fragment key={group.label}>
              <DropdownMenuLabel className="text-xs font-normal text-muted-foreground">
                {group.label}
              </DropdownMenuLabel>
              {group.sessions.slice(0, 5).map(session => (
                <DropdownMenuItem
                  key={session.id}
                  onClick={() => handleSessionSelect(session)}
                  className="p-0"
                >
                  {renderSessionItem(session, true)}
                </DropdownMenuItem>
              ))}
            </React.Fragment>
          ))}
          
          {showCreateButton && onCreateNew && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={onCreateNew} className="text-primary">
                <Plus className="h-3 w-3 mr-2" />
                Create New Session
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              {renderCurrentSessionDisplay()}
            </div>
            
            <div className="flex items-center gap-2">
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm">
                    Switch
                    <ChevronDown className="h-3 w-3 ml-1" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-96 p-0" align="end">
                  <Command>
                    <CommandInput 
                      placeholder="Search sessions..." 
                      value={searchQuery}
                      onValueChange={setSearchQuery}
                    />
                    <CommandList className="max-h-96">
                      <CommandEmpty>No sessions found.</CommandEmpty>
                      
                      {sessionGroups.map(group => (
                        <CommandGroup key={group.label} heading={group.label}>
                          {group.sessions.map(session => (
                            <CommandItem
                              key={session.id}
                              value={session.title}
                              onSelect={() => handleSessionSelect(session)}
                              className="p-0"
                            >
                              {renderSessionItem(session)}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      ))}
                      
                      {showCreateButton && onCreateNew && (
                        <CommandGroup>
                          <CommandItem onSelect={onCreateNew} className="text-primary">
                            <Plus className="h-3 w-3 mr-2" />
                            Create New Session
                          </CommandItem>
                        </CommandGroup>
                      )}
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              
              {showCreateButton && onCreateNew && (
                <Button size="sm" onClick={onCreateNew}>
                  <Plus className="h-3 w-3 mr-1" />
                  New
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      {variant === 'full' && currentSession && (
        <div className="grid grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-3">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="text-lg font-semibold">{currentSession.messages.length}</div>
                  <div className="text-xs text-muted-foreground">Messages</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-3">
              <div className="flex items-center gap-2">
                <Lightbulb className="h-4 w-4 text-yellow-500" />
                <div>
                  <div className="text-lg font-semibold">
                    {getIdeasBySession(currentSession.id).length}
                  </div>
                  <div className="text-xs text-muted-foreground">Ideas</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-3">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-green-500" />
                <div>
                  <div className="text-lg font-semibold">
                    {Math.ceil((Date.now() - new Date(currentSession.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
                  </div>
                  <div className="text-xs text-muted-foreground">Days</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};