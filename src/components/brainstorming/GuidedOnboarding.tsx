/**
 * Guided Onboarding Component
 * 
 * Provides step-by-step introduction to brainstorming mode features
 * with interactive tutorials and contextual help.
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowRight,
  ArrowLeft,
  X,
  CheckCircle,
  Lightbulb,
  Users,
  Target,
  Brain,
  Palette,
  Play,
  Skip,
  HelpCircle,
  BookOpen,
  Zap,
  Star
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  content: React.ReactNode;
  icon: React.ComponentType<any>;
  duration: string;
  interactive?: boolean;
  targetElement?: string;
}

const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Brainstorming Mode',
    description: 'A dedicated creative space for generating and organizing ideas',
    duration: '1 min',
    icon: Brain,
    content: (
      <div className="space-y-4">
        <div className="text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="inline-block mb-4"
          >
            <Brain className="w-16 h-16 text-purple-600" />
          </motion.div>
          <h3 className="text-xl font-bold mb-2">Welcome to Your Creative Space!</h3>
          <p className="text-gray-600">
            Brainstorming Mode is completely separate from Claude Code sessions, 
            giving you a focused environment for creative thinking and idea generation.
          </p>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mt-6">
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <Lightbulb className="w-8 h-8 text-purple-600 mx-auto mb-2" />
            <h4 className="font-medium">Generate Ideas</h4>
            <p className="text-sm text-gray-600">Unlimited creative brainstorming</p>
          </div>
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <h4 className="font-medium">Collaborate</h4>
            <p className="text-sm text-gray-600">Real-time team collaboration</p>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'navigation',
    title: 'Navigation & Features',
    description: 'Learn about the dedicated brainstorming navigation and tools',
    duration: '2 min',
    icon: Target,
    content: (
      <div className="space-y-4">
        <h3 className="text-lg font-bold">Explore Your Tools</h3>
        <div className="space-y-3">
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <Brain className="w-4 h-4 text-purple-600" />
            </div>
            <div>
              <h4 className="font-medium">Sessions</h4>
              <p className="text-sm text-gray-600">Manage your brainstorming sessions</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <Lightbulb className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h4 className="font-medium">Ideas</h4>
              <p className="text-sm text-gray-600">View and organize all your ideas</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <Users className="w-4 h-4 text-green-600" />
            </div>
            <div>
              <h4 className="font-medium">Collaboration</h4>
              <p className="text-sm text-gray-600">Work together with your team</p>
            </div>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'templates',
    title: 'Template-Based Sessions',
    description: 'Use structured approaches like SWOT, Design Thinking, and more',
    duration: '2 min',
    icon: Palette,
    content: (
      <div className="space-y-4">
        <h3 className="text-lg font-bold">Choose Your Approach</h3>
        <p className="text-gray-600">
          Templates provide structured frameworks to guide your brainstorming sessions.
        </p>
        
        <div className="grid grid-cols-1 gap-3">
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <span className="text-blue-600 font-bold">SW</span>
            </div>
            <div>
              <h4 className="font-medium">SWOT Analysis</h4>
              <p className="text-sm text-gray-600">Analyze strengths, weaknesses, opportunities, threats</p>
            </div>
            <Badge variant="secondary">Beginner</Badge>
          </div>
          
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Palette className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <h4 className="font-medium">Design Thinking</h4>
              <p className="text-sm text-gray-600">Human-centered innovation process</p>
            </div>
            <Badge variant="secondary">Intermediate</Badge>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'collaboration',
    title: 'Real-Time Collaboration',
    description: 'Work together with live cursors, chat, and shared editing',
    duration: '2 min',
    icon: Users,
    content: (
      <div className="space-y-4">
        <h3 className="text-lg font-bold">Collaborate in Real-Time</h3>
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
              <CheckCircle className="w-4 h-4 text-green-600" />
            </div>
            <div>
              <h4 className="font-medium">Live Presence</h4>
              <p className="text-sm text-gray-600">See who's online and what they're working on</p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-1">
              <CheckCircle className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h4 className="font-medium">Shared Ideas</h4>
              <p className="text-sm text-gray-600">Ideas update instantly for all participants</p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mt-1">
              <CheckCircle className="w-4 h-4 text-purple-600" />
            </div>
            <div>
              <h4 className="font-medium">Team Chat</h4>
              <p className="text-sm text-gray-600">Communicate without leaving the session</p>
            </div>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'ready',
    title: 'You\'re Ready to Start!',
    description: 'Begin your first brainstorming session',
    duration: '1 min',
    icon: Star,
    content: (
      <div className="text-center space-y-4">
        <motion.div
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <Star className="w-16 h-16 text-yellow-500 mx-auto" />
        </motion.div>
        
        <h3 className="text-xl font-bold">You're All Set!</h3>
        <p className="text-gray-600">
          You now know the basics of Brainstorming Mode. Ready to create your first session?
        </p>
        
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg">
          <h4 className="font-medium mb-2">Quick Tips:</h4>
          <ul className="text-sm text-gray-600 space-y-1 text-left">
            <li>• Start with a template for structured brainstorming</li>
            <li>• Invite team members for collaborative sessions</li>
            <li>• Use the idea map to visualize connections</li>
            <li>• Track progress with built-in metrics</li>
          </ul>
        </div>
      </div>
    )
  }
];

interface GuidedOnboardingProps {
  onComplete: () => void;
  onSkip: () => void;
  className?: string;
}

export const GuidedOnboarding: React.FC<GuidedOnboardingProps> = ({
  onComplete,
  onSkip,
  className
}) => {
  const { theme } = useBrainstormingTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const step = ONBOARDING_STEPS[currentStep];
  const progress = ((currentStep + 1) / ONBOARDING_STEPS.length) * 100;

  const handleNext = () => {
    if (currentStep < ONBOARDING_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    setIsVisible(false);
    setTimeout(onComplete, 300);
  };

  const handleSkip = () => {
    setIsVisible(false);
    setTimeout(onSkip, 300);
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm",
        className
      )}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="w-full max-w-2xl mx-4"
      >
        <Card className="shadow-2xl">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div 
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: theme.colors.primary + '20' }}
                >
                  <step.icon 
                    className="w-6 h-6" 
                    style={{ color: theme.colors.primary }}
                  />
                </div>
                <div>
                  <CardTitle>{step.title}</CardTitle>
                  <CardDescription>{step.description}</CardDescription>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Badge variant="outline">{step.duration}</Badge>
                <Button variant="ghost" size="sm" onClick={handleSkip}>
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span style={{ color: theme.colors.text }}>
                  Step {currentStep + 1} of {ONBOARDING_STEPS.length}
                </span>
                <span style={{ color: theme.colors.textSecondary }}>
                  {Math.round(progress)}% complete
                </span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="min-h-[300px] mb-6">
              <AnimatePresence mode="wait">
                <motion.div
                  key={step.id}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {step.content}
                </motion.div>
              </AnimatePresence>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentStep === 0}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Previous
                </Button>
                
                <Button variant="ghost" onClick={handleSkip}>
                  <Skip className="w-4 h-4 mr-2" />
                  Skip Tutorial
                </Button>
              </div>
              
              <Button onClick={handleNext}>
                {currentStep === ONBOARDING_STEPS.length - 1 ? (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Start Brainstorming
                  </>
                ) : (
                  <>
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
};

/**
 * Contextual Help Component
 * Provides in-context help and tips based on current activity
 */
interface ContextualHelpProps {
  context: 'dashboard' | 'session' | 'collaboration' | 'idea-map' | 'templates';
  className?: string;
}

export const ContextualHelp: React.FC<ContextualHelpProps> = ({
  context,
  className
}) => {
  const { theme } = useBrainstormingTheme();
  const [isExpanded, setIsExpanded] = useState(false);

  const getHelpContent = () => {
    switch (context) {
      case 'dashboard':
        return {
          title: 'Dashboard Help',
          tips: [
            'View all your brainstorming sessions at a glance',
            'Use the search bar to find specific sessions',
            'Filter by template type or tags',
            'Click "New Session" to start brainstorming'
          ]
        };
      case 'session':
        return {
          title: 'Session Help',
          tips: [
            'Type your ideas in the chat interface',
            'Ideas are automatically extracted and organized',
            'Use @mentions to reference other participants',
            'Switch between different view modes'
          ]
        };
      case 'collaboration':
        return {
          title: 'Collaboration Help',
          tips: [
            'See who\'s online in the participants panel',
            'Use the chat for quick communication',
            'Watch the activity feed for real-time updates',
            'Invite others using the invite button'
          ]
        };
      case 'idea-map':
        return {
          title: 'Idea Map Help',
          tips: [
            'Drag ideas to organize them spatially',
            'Double-click empty space to create new ideas',
            'Select multiple ideas to create clusters',
            'Use the toolbar to change view modes'
          ]
        };
      case 'templates':
        return {
          title: 'Templates Help',
          tips: [
            'Choose a template that fits your goal',
            'Templates provide structured guidance',
            'Follow the step-by-step process',
            'Customize the session to your needs'
          ]
        };
      default:
        return { title: 'Help', tips: [] };
    }
  };

  const helpContent = getHelpContent();

  return (
    <div className={cn("fixed bottom-4 right-4 z-40", className)}>
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            className="mb-2"
          >
            <Card className="w-80 shadow-lg">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <BookOpen className="w-4 h-4" style={{ color: theme.colors.primary }} />
                  {helpContent.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <ul className="space-y-2">
                  {helpContent.tips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm">
                      <Zap className="w-3 h-3 mt-0.5 flex-shrink-0" style={{ color: theme.colors.accent }} />
                      <span style={{ color: theme.colors.text }}>{tip}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
      
      <Button
        onClick={() => setIsExpanded(!isExpanded)}
        className="rounded-full w-12 h-12 shadow-lg"
        style={{ backgroundColor: theme.colors.primary }}
      >
        <HelpCircle className="w-5 h-5" />
      </Button>
    </div>
  );
};
