/**
 * Brainstorming Session Manager Component
 * 
 * Dedicated session management for brainstorming workflows.
 * Provides session creation, switching, lifecycle management, and isolation from regular coding sessions.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Brain,
  Calendar,
  Clock,
  Users,
  Tag,
  MoreHorizontal,
  Edit2,
  Trash2,
  Copy,
  Download,
  Upload,
  FolderOpen,
  Archive,
  RefreshCw,
  Search,
  Filter,
  SortAsc,
  Star,
  MessageSquare,
  Lightbulb,
  Target,
  CheckCircle,
  AlertCircle,
  Pause,
  Play
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogT<PERSON>le,
  Dialog<PERSON>rigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import { cn } from '@/lib/utils';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { BrainstormSession, TemplateType, ViewType } from '@/types/brainstorm';
import { brainstormTemplates } from '@/lib/brainstorm-templates';
import { flowEngine } from '@/lib/brainstorming/flowEngine';

interface BrainstormingSessionManagerProps {
  currentSessionId?: string;
  onSessionSelect: (session: BrainstormSession) => void;
  onSessionCreate?: (session: BrainstormSession) => void;
  onSessionDelete?: (sessionId: string) => void;
  className?: string;
  showCreateButton?: boolean;
  compactMode?: boolean;
  maxSessions?: number;
}

interface SessionFormData {
  title: string;
  description: string;
  template?: TemplateType;
  tags: string[];
  goals: string[];
}

interface SessionFilter {
  search: string;
  template?: TemplateType;
  sortBy: 'recent' | 'alphabetical' | 'ideas' | 'activity';
  showArchived: boolean;
}

const TEMPLATE_ICONS: Record<TemplateType, string> = {
  [TemplateType.SWOT]: '📊',
  [TemplateType.FIVE_WHYS]: '❓',
  [TemplateType.DESIGN_THINKING]: '🎨',
  [TemplateType.CANVAS]: '🗺️',
  [TemplateType.CUSTOM]: '✨'
};

export const BrainstormingSessionManager: React.FC<BrainstormingSessionManagerProps> = ({
  currentSessionId,
  onSessionSelect,
  onSessionCreate,
  onSessionDelete,
  className,
  showCreateButton = true,
  compactMode = false,
  maxSessions = 50
}) => {
  const {
    sessions,
    currentSessionId: storeSessionId,
    createSession,
    updateSession,
    deleteSession,
    setCurrentSession,
    getIdeasBySession,
    exportSession,
    importSession
  } = useBrainstormStore();

  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [sessionForm, setSessionForm] = useState<SessionFormData>({
    title: '',
    description: '',
    template: undefined,
    tags: [],
    goals: []
  });
  const [filter, setFilter] = useState<SessionFilter>({
    search: '',
    sortBy: 'recent',
    showArchived: false
  });
  const [editingSession, setEditingSession] = useState<string | null>(null);
  const [importData, setImportData] = useState('');

  const sessionList = Object.values(sessions);
  const effectiveCurrentSessionId = currentSessionId || storeSessionId;

  // Filter and sort sessions
  const filteredSessions = sessionList
    .filter(session => {
      if (!filter.showArchived && session.metadata?.archived) return false;
      if (filter.search && !session.title.toLowerCase().includes(filter.search.toLowerCase())) return false;
      if (filter.template && session.template !== filter.template) return false;
      return true;
    })
    .sort((a, b) => {
      switch (filter.sortBy) {
        case 'alphabetical':
          return a.title.localeCompare(b.title);
        case 'ideas':
          const aIdeas = getIdeasBySession(a.id).length;
          const bIdeas = getIdeasBySession(b.id).length;
          return bIdeas - aIdeas;
        case 'activity':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        case 'recent':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    })
    .slice(0, maxSessions);

  const handleCreateSession = useCallback(async () => {
    if (!sessionForm.title.trim()) return;

    try {
      const sessionId = createSession(
        sessionForm.title,
        sessionForm.template
      );

      // Update session with additional metadata
      updateSession(sessionId, {
        metadata: {
          ...sessions[sessionId]?.metadata,
          description: sessionForm.description,
          goals: sessionForm.goals,
          createdWith: 'session-manager'
        },
        tags: sessionForm.tags
      });

      const newSession = sessions[sessionId];
      if (newSession) {
        onSessionCreate?.(newSession);
        onSessionSelect(newSession);
      }

      // Reset form
      setSessionForm({
        title: '',
        description: '',
        template: undefined,
        tags: [],
        goals: []
      });
      setShowCreateDialog(false);
    } catch (error) {
      console.error('Failed to create session:', error);
    }
  }, [sessionForm, createSession, updateSession, sessions, onSessionCreate, onSessionSelect]);

  const handleSessionClick = useCallback((session: BrainstormSession) => {
    setCurrentSession(session.id);
    onSessionSelect(session);
  }, [setCurrentSession, onSessionSelect]);

  const handleDeleteSession = useCallback(async (sessionId: string) => {
    try {
      deleteSession(sessionId);
      onSessionDelete?.(sessionId);
    } catch (error) {
      console.error('Failed to delete session:', error);
    }
  }, [deleteSession, onSessionDelete]);

  const handleExportSession = useCallback(async (sessionId: string) => {
    try {
      await exportSession(sessionId, 'json');
    } catch (error) {
      console.error('Failed to export session:', error);
    }
  }, [exportSession]);

  const handleImportSession = useCallback(async () => {
    if (!importData.trim()) return;

    try {
      const sessionId = await importSession(importData);
      const importedSession = sessions[sessionId];
      if (importedSession) {
        onSessionSelect(importedSession);
      }
      
      setImportData('');
      setShowImportDialog(false);
    } catch (error) {
      console.error('Failed to import session:', error);
    }
  }, [importData, importSession, sessions, onSessionSelect]);

  const handleArchiveSession = useCallback((sessionId: string) => {
    updateSession(sessionId, {
      metadata: {
        ...sessions[sessionId]?.metadata,
        archived: true,
        archivedAt: new Date().toISOString()
      }
    });
  }, [updateSession, sessions]);

  const handleDuplicateSession = useCallback((session: BrainstormSession) => {
    const duplicatedSessionId = createSession(
      `${session.title} (Copy)`,
      session.template
    );

    updateSession(duplicatedSessionId, {
      metadata: {
        ...session.metadata,
        duplicatedFrom: session.id,
        duplicatedAt: new Date().toISOString()
      },
      tags: session.tags
    });

    const duplicatedSession = sessions[duplicatedSessionId];
    if (duplicatedSession) {
      onSessionSelect(duplicatedSession);
    }
  }, [createSession, updateSession, sessions, onSessionSelect]);

  const renderSessionCard = (session: BrainstormSession) => {
    const isActive = session.id === effectiveCurrentSessionId;
    const ideas = getIdeasBySession(session.id);
    const isEditing = editingSession === session.id;
    
    // Check if session has active flow
    const flowProgress = flowEngine.getFlowProgress(session.id);
    
    return (
      <motion.div
        key={session.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        whileHover={{ scale: 1.02 }}
        className={cn(
          "group transition-all duration-200",
          compactMode && "mb-2"
        )}
      >
        <Card 
          className={cn(
            "cursor-pointer transition-all duration-200 hover:shadow-md",
            isActive && "border-primary shadow-lg",
            session.metadata?.archived && "opacity-60"
          )}
          onClick={() => !isEditing && handleSessionClick(session)}
        >
          <CardHeader className={cn("pb-3", compactMode && "pb-2")}>
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3 flex-1">
                <div className="flex items-center gap-2">
                  {session.template && (
                    <span className="text-lg">
                      {TEMPLATE_ICONS[session.template]}
                    </span>
                  )}
                  <Brain className={cn(
                    "h-4 w-4",
                    isActive ? "text-primary" : "text-muted-foreground"
                  )} />
                </div>
                
                <div className="flex-1 min-w-0">
                  {isEditing ? (
                    <Input
                      value={session.title}
                      onChange={(e) => updateSession(session.id, { title: e.target.value })}
                      onBlur={() => setEditingSession(null)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') setEditingSession(null);
                        if (e.key === 'Escape') setEditingSession(null);
                      }}
                      className="text-sm font-medium"
                      autoFocus
                      onClick={(e) => e.stopPropagation()}
                    />
                  ) : (
                    <CardTitle className={cn(
                      "text-sm font-medium leading-tight truncate",
                      compactMode && "text-xs"
                    )}>
                      {session.title}
                    </CardTitle>
                  )}
                  
                  {!compactMode && session.metadata?.description && (
                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                      {session.metadata.description}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2 ml-2">
                {/* Active Flow Indicator */}
                {flowProgress && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center">
                          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Active flow: {flowProgress.flowName}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {/* Session Actions */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreHorizontal className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Session Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => setEditingSession(session.id)}>
                      <Edit2 className="h-3 w-3 mr-2" />
                      Rename
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleDuplicateSession(session)}>
                      <Copy className="h-3 w-3 mr-2" />
                      Duplicate
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleExportSession(session.id)}>
                      <Download className="h-3 w-3 mr-2" />
                      Export
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleArchiveSession(session.id)}>
                      <Archive className="h-3 w-3 mr-2" />
                      Archive
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => handleDeleteSession(session.id)}
                      className="text-destructive"
                    >
                      <Trash2 className="h-3 w-3 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardHeader>

          {!compactMode && (
            <CardContent className="pt-0">
              <div className="space-y-3">
                {/* Session Stats */}
                <div className="flex items-center gap-4 text-xs">
                  <div className="flex items-center gap-1">
                    <MessageSquare className="h-3 w-3" />
                    <span>{session.messages.length} messages</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Lightbulb className="h-3 w-3" />
                    <span>{ideas.length} ideas</span>
                  </div>
                  
                  {session.metadata?.totalClusters > 0 && (
                    <div className="flex items-center gap-1">
                      <Target className="h-3 w-3" />
                      <span>{session.metadata.totalClusters} clusters</span>
                    </div>
                  )}
                </div>

                {/* Tags */}
                {session.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {session.tags.slice(0, 3).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {session.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{session.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}

                {/* Goals Progress */}
                {session.metadata?.goals && session.metadata.goals.length > 0 && (
                  <div className="space-y-1">
                    <div className="text-xs text-muted-foreground">Goals Progress</div>
                    <div className="space-y-1">
                      {session.metadata.goals.slice(0, 2).map((goal, index) => (
                        <div key={index} className="flex items-center gap-2 text-xs">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span className="truncate">{goal}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Timestamps */}
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{new Date(session.createdAt).toLocaleDateString()}</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{new Date(session.updatedAt).toLocaleTimeString()}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          )}
        </Card>
      </motion.div>
    );
  };

  const renderCreateDialog = () => (
    <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create New Brainstorming Session</DialogTitle>
          <DialogDescription>
            Set up a new brainstorming session with your preferred settings
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Session Title</Label>
            <Input
              id="title"
              value={sessionForm.title}
              onChange={(e) => setSessionForm(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Enter session title..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={sessionForm.description}
              onChange={(e) => setSessionForm(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe what you want to brainstorm about..."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="template">Template (Optional)</Label>
            <Select 
              value={sessionForm.template} 
              onValueChange={(value) => setSessionForm(prev => ({ 
                ...prev, 
                template: value as TemplateType 
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose a template" />
              </SelectTrigger>
              <SelectContent>
                {brainstormTemplates.map(template => (
                  <SelectItem key={template.id} value={template.id}>
                    <div className="flex items-center gap-2">
                      <span>{template.icon}</span>
                      <span>{template.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="tags">Tags (Optional)</Label>
            <Input
              id="tags"
              value={sessionForm.tags.join(', ')}
              onChange={(e) => setSessionForm(prev => ({ 
                ...prev, 
                tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
              }))}
              placeholder="Enter tags separated by commas..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="goals">Goals (Optional)</Label>
            <Textarea
              id="goals"
              value={sessionForm.goals.join('\n')}
              onChange={(e) => setSessionForm(prev => ({ 
                ...prev, 
                goals: e.target.value.split('\n').filter(Boolean)
              }))}
              placeholder="Enter each goal on a new line..."
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setShowCreateDialog(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={handleCreateSession}
            disabled={!sessionForm.title.trim()}
          >
            Create Session
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  const renderImportDialog = () => (
    <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Import Session</DialogTitle>
          <DialogDescription>
            Paste your exported session data to import it
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Textarea
            value={importData}
            onChange={(e) => setImportData(e.target.value)}
            placeholder="Paste your session JSON data here..."
            rows={10}
          />
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setShowImportDialog(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={handleImportSession}
            disabled={!importData.trim()}
          >
            Import
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">Brainstorming Sessions</h2>
          <p className="text-sm text-muted-foreground">
            {filteredSessions.length} session{filteredSessions.length !== 1 ? 's' : ''}
          </p>
        </div>

        <div className="flex items-center gap-2">
          {showCreateButton && (
            <Button
              size="sm"
              onClick={() => setShowCreateDialog(true)}
            >
              <Plus className="h-3 w-3 mr-1" />
              New Session
            </Button>
          )}
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setShowImportDialog(true)}>
                <Upload className="h-3 w-3 mr-2" />
                Import Session
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Filters */}
      {!compactMode && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-3 w-3 text-muted-foreground" />
                  <Input
                    placeholder="Search sessions..."
                    value={filter.search}
                    onChange={(e) => setFilter(prev => ({ ...prev, search: e.target.value }))}
                    className="pl-7 text-sm"
                  />
                </div>
              </div>

              <Select 
                value={filter.sortBy} 
                onValueChange={(value) => setFilter(prev => ({ 
                  ...prev, 
                  sortBy: value as SessionFilter['sortBy']
                }))}
              >
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recent">Recent</SelectItem>
                  <SelectItem value="alphabetical">A-Z</SelectItem>
                  <SelectItem value="ideas">Most Ideas</SelectItem>
                  <SelectItem value="activity">Last Activity</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setFilter(prev => ({ ...prev, showArchived: !prev.showArchived }))}
              >
                <Archive className="h-3 w-3 mr-1" />
                {filter.showArchived ? 'Hide' : 'Show'} Archived
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sessions List */}
      <div className={cn(
        "space-y-3",
        compactMode && "space-y-2"
      )}>
        <AnimatePresence>
          {filteredSessions.map(session => renderSessionCard(session))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {filteredSessions.length === 0 && (
        <Card className="text-center py-8">
          <CardContent>
            <Brain className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="font-medium mb-2">No sessions found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              {filter.search ? 
                'No sessions match your search criteria' : 
                'Create your first brainstorming session to get started'
              }
            </p>
            {showCreateButton && !filter.search && (
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-3 w-3 mr-1" />
                Create Session
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Dialogs */}
      {renderCreateDialog()}
      {renderImportDialog()}
    </div>
  );
};