/**
 * Enhanced Brainstorming Interface
 * 
 * Main integration component that brings together all enhanced brainstorming features
 * including chat, visualizations, AI personas, memory insights, and more.
 */

import React, { useState, useEffect } from 'react';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { ViewType, TemplateType } from '@/types/brainstorm';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
// Using simple flex layout instead of resizable panels
import {
  MessageSquare,
  GitBranch,
  Columns3,
  Grid3x3,
  FileText,
  Brain,
  Mic,
  Search,
  Settings,
  Sparkles,
  Users,
  Save,
  Share2,
  Download,
  Plus,
  Clock,
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Import all brainstorming components
import {
  EnhancedBrainstormingChat,
  MindMapView,
  Ka<PERSON>banView,
  MatrixView,
  TemplateSelector,
  TaskGenerator,
  MultiModalInput,
  PersonaManager,
  PersonaSwitcher,
  MemoryInsights,
  SearchInterface,
  VoiceInterface,
  ClusterManager,
  IdeaManager,
  BrainstormErrorBoundary,
} from './index';
import { BrainstormingSessionHistory } from './BrainstormingSessionHistory';
import { sessionRestoration, useSessionRestoration } from '@/lib/brainstorming/sessionRestoration';

interface EnhancedBrainstormingInterfaceProps {
  sessionId?: string;
  className?: string;
}

const viewIcons = {
  [ViewType.CHAT]: MessageSquare,
  [ViewType.MIND_MAP]: GitBranch,
  [ViewType.KANBAN]: Columns3,
  [ViewType.MATRIX]: Grid3x3,
  [ViewType.TEMPLATES]: FileText,
};

export const EnhancedBrainstormingInterface: React.FC<EnhancedBrainstormingInterfaceProps> = ({
  sessionId: propSessionId,
  className,
}) => {
  const {
    currentSessionId,
    currentView,
    setCurrentView,
    createSession,
    getCurrentSession,
    getIdeasBySession,
    settings,
  } = useBrainstormStore();

  const [activeSessionId, setActiveSessionId] = useState<string | null>(propSessionId || currentSessionId);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [showSidePanels, setShowSidePanels] = useState(true);
  const [activeSidePanel, setActiveSidePanel] = useState<'personas' | 'memory' | 'search' | 'voice' | 'clusters' | 'history'>('personas');

  const session = getCurrentSession();
  const ideas = activeSessionId ? getIdeasBySession(activeSessionId) : [];

  // Session restoration
  const sessionRestorationHooks = useSessionRestoration(activeSessionId || '');

  // Handle session switching and restoration
  const handleSessionSelect = (selectedSession: BrainstormSession) => {
    if (activeSessionId) {
      // Save current session state before switching
      sessionRestorationHooks.saveState({
        sessionId: activeSessionId,
        lastActivity: new Date().toISOString(),
        activeView: currentView,
        sidebarState: {
          isOpen: showSidePanels,
          activePanel: activeSidePanel
        }
      });
    }

    // Switch to new session
    setActiveSessionId(selectedSession.id);
    setCurrentSession(selectedSession.id);

    // Restore session state
    const restoredState = sessionRestorationHooks.restoreState();
    if (restoredState) {
      setCurrentView(restoredState.activeView as ViewType);
      setShowSidePanels(restoredState.sidebarState.isOpen);
      setActiveSidePanel(restoredState.sidebarState.activePanel as any);
    }
  };

  const handleSessionResume = (selectedSession: BrainstormSession) => {
    handleSessionSelect(selectedSession);

    // Check if session should auto-resume
    if (sessionRestorationHooks.shouldAutoResume()) {
      const continuationPrompt = sessionRestorationHooks.getContinuationPrompt();
      if (continuationPrompt) {
        console.log('Auto-resuming session with prompt:', continuationPrompt);
        // This would trigger the chat to continue with the saved prompt
      }
    }
  };

  // Create a new session if none exists
  useEffect(() => {
    if (!activeSessionId && !currentSessionId) {
      const newSessionId = createSession('New Brainstorming Session');
      setActiveSessionId(newSessionId);
    } else if (currentSessionId && !activeSessionId) {
      setActiveSessionId(currentSessionId);
    }
  }, [activeSessionId, currentSessionId, createSession]);

  const handleTemplateSelect = (template: any) => {
    const newSessionId = createSession(
      `${template.name} Session`,
      template.type as TemplateType
    );
    setActiveSessionId(newSessionId);
    setShowTemplateSelector(false);
  };

  const handleViewChange = (view: ViewType) => {
    setCurrentView(view);
  };

  const handleVoiceCommand = (command: string) => {
    switch (command) {
      case 'SWITCH_KANBAN':
        setCurrentView(ViewType.KANBAN);
        break;
      case 'SWITCH_MINDMAP':
        setCurrentView(ViewType.MIND_MAP);
        break;
      case 'SWITCH_MATRIX':
        setCurrentView(ViewType.MATRIX);
        break;
      case 'SWITCH_CHAT':
        setCurrentView(ViewType.CHAT);
        break;
      case 'EXPORT_SESSION':
        // Handle export
        console.log('Export session via voice command');
        break;
      case 'SHOW_HELP':
        // Show help
        console.log('Show help via voice command');
        break;
      default:
        console.log('Unknown voice command:', command);
    }
  };

  if (!activeSessionId) {
    return (
      <div className="flex items-center justify-center h-full">
        <Card>
          <CardContent className="p-6 text-center">
            <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-semibold mb-2">Start Brainstorming</h3>
            <p className="text-muted-foreground mb-4">
              Create a new session or select a template to begin.
            </p>
            <div className="flex gap-2 justify-center">
              <Button onClick={() => setActiveSessionId(createSession())}>
                <Plus className="h-4 w-4 mr-2" />
                New Session
              </Button>
              <Button variant="outline" onClick={() => setShowTemplateSelector(true)}>
                <FileText className="h-4 w-4 mr-2" />
                Use Template
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <BrainstormErrorBoundary>
      <div className={cn("enhanced-brainstorming-interface h-full flex flex-col", className)}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Brain className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-semibold">
                {session?.title || 'Brainstorming Session'}
              </h1>
              <Badge variant="secondary">{ideas.length} ideas</Badge>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSidePanels(!showSidePanels)}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex">
          {/* Main View */}
          <div className={cn("flex flex-col", showSidePanels ? "flex-1" : "w-full")}>
                {/* View Tabs */}
                <Tabs value={currentView} onValueChange={handleViewChange} className="flex-1 flex flex-col">
                  <TabsList className="grid w-full grid-cols-5 mx-4 mt-4">
                    {Object.entries(ViewType).map(([key, value]) => {
                      const IconComponent = viewIcons[value];
                      return (
                        <TabsTrigger key={value} value={value} className="flex items-center gap-2">
                          <IconComponent className="h-4 w-4" />
                          <span className="hidden sm:inline">
                            {key.charAt(0) + key.slice(1).toLowerCase().replace('_', ' ')}
                          </span>
                        </TabsTrigger>
                      );
                    })}
                  </TabsList>

                  {/* View Content */}
                  <div className="flex-1 p-4">
                    <TabsContent value={ViewType.CHAT} className="h-full m-0">
                      {activeSessionId ? (
                        <EnhancedBrainstormingChat sessionId={activeSessionId} />
                      ) : (
                        <div className="flex items-center justify-center h-full text-muted-foreground">
                          <p>No session available</p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value={ViewType.MIND_MAP} className="h-full m-0">
                      {activeSessionId ? (
                        <MindMapView sessionId={activeSessionId} />
                      ) : (
                        <div className="flex items-center justify-center h-full text-muted-foreground">
                          <p>No session available</p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value={ViewType.KANBAN} className="h-full m-0">
                      {activeSessionId ? (
                        <KanbanView sessionId={activeSessionId} />
                      ) : (
                        <div className="flex items-center justify-center h-full text-muted-foreground">
                          <p>No session available</p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value={ViewType.MATRIX} className="h-full m-0">
                      {activeSessionId ? (
                        <MatrixView sessionId={activeSessionId} />
                      ) : (
                        <div className="flex items-center justify-center h-full text-muted-foreground">
                          <p>No session available</p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value={ViewType.TEMPLATES} className="h-full m-0">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
                        <TemplateSelector
                          open={true}
                          onOpenChange={() => {}}
                          onSelectTemplate={handleTemplateSelect}
                        />
                        {activeSessionId ? (
                          <TaskGenerator sessionId={activeSessionId} />
                        ) : (
                          <div className="flex items-center justify-center h-full text-muted-foreground">
                            <p>No session available</p>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  </div>
                </Tabs>
              </div>

            {/* Side Panels */}
            {showSidePanels && (
              <div className="w-80 h-full flex flex-col border-l bg-background/50">
                    {/* Side Panel Tabs */}
                    <div className="flex border-b">
                      {[
                        { id: 'personas', icon: Sparkles, label: 'Personas' },
                        { id: 'history', icon: Clock, label: 'History' },
                        { id: 'memory', icon: Brain, label: 'Memory' },
                        { id: 'search', icon: Search, label: 'Search' },
                        { id: 'voice', icon: Mic, label: 'Voice' },
                        { id: 'clusters', icon: GitBranch, label: 'Clusters' },
                      ].map(({ id, icon: Icon, label }) => (
                        <Button
                          key={id}
                          variant={activeSidePanel === id ? "default" : "ghost"}
                          size="sm"
                          onClick={() => setActiveSidePanel(id as any)}
                          className="flex-1 rounded-none"
                        >
                          <Icon className="h-4 w-4" />
                          <span className="hidden lg:inline ml-2">{label}</span>
                        </Button>
                      ))}
                    </div>

                    {/* Side Panel Content */}
                    <div className="flex-1 p-4 overflow-auto">
                      {activeSidePanel === 'personas' && (
                        <PersonaManager
                          compactMode={true}
                          onPersonaSelect={(persona) => {
                            console.log('Selected persona:', persona);
                          }}
                        />
                      )}
                      {activeSidePanel === 'history' && (
                        <BrainstormingSessionHistory
                          onSessionSelect={handleSessionSelect}
                          onSessionResume={handleSessionResume}
                          currentSessionId={activeSessionId}
                          compactMode={true}
                        />
                      )}
                      {activeSidePanel === 'memory' && activeSessionId && (
                        <MemoryInsights sessionId={activeSessionId} />
                      )}
                      {activeSidePanel === 'search' && (
                        <SearchInterface />
                      )}
                      {activeSidePanel === 'voice' && (
                        <VoiceInterface onCommand={handleVoiceCommand} />
                      )}
                      {activeSidePanel === 'clusters' && activeSessionId && (
                        <ClusterManager sessionId={activeSessionId} />
                      )}
                    </div>
                  </div>
            )}
        </div>

        {/* Template Selector Modal */}
        <TemplateSelector
          open={showTemplateSelector}
          onOpenChange={setShowTemplateSelector}
          onSelectTemplate={handleTemplateSelect}
        />
      </div>
    </BrainstormErrorBoundary>
  );
};