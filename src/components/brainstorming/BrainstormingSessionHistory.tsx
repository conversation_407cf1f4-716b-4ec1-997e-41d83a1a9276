/**
 * Brainstorming Session History Component
 * 
 * Displays and manages brainstorming session history with search, filtering, and restoration
 */

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Clock,
  Search,
  Filter,
  Play,
  Pause,
  Archive,
  Trash2,
  Download,
  Upload,
  Star,
  StarOff,
  MessageSquare,
  Lightbulb,
  Users,
  Calendar,
  Tag,
  MoreHorizontal,
  Eye,
  Edit,
  Copy
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

import { cn } from '@/lib/utils';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';
import { BrainstormSession, TemplateType } from '@/types/brainstorm';

interface BrainstormingSessionHistoryProps {
  onSessionSelect: (session: BrainstormSession) => void;
  onSessionResume: (session: BrainstormSession) => void;
  currentSessionId?: string;
  className?: string;
  compactMode?: boolean;
}

export const BrainstormingSessionHistory: React.FC<BrainstormingSessionHistoryProps> = ({
  onSessionSelect,
  onSessionResume,
  currentSessionId,
  className,
  compactMode = false
}) => {
  const { theme } = useBrainstormingTheme();
  const {
    sessions,
    deleteSession,
    updateSession,
    exportSession,
    getIdeasBySession,
    setCurrentSession
  } = useBrainstormStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [filterBy, setFilterBy] = useState<'all' | 'recent' | 'starred' | 'archived'>('all');
  const [sortBy, setSortBy] = useState<'updated' | 'created' | 'title' | 'ideas'>('updated');
  const [selectedSessions, setSelectedSessions] = useState<Set<string>>(new Set());

  const sessionList = Object.values(sessions);

  const filteredAndSortedSessions = useMemo(() => {
    let filtered = sessionList;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(session =>
        session.title.toLowerCase().includes(query) ||
        session.tags?.some(tag => tag.toLowerCase().includes(query)) ||
        session.metadata?.description?.toLowerCase().includes(query)
      );
    }

    // Apply category filter
    switch (filterBy) {
      case 'recent':
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        filtered = filtered.filter(session => new Date(session.updatedAt) > weekAgo);
        break;
      case 'starred':
        filtered = filtered.filter(session => session.metadata?.starred);
        break;
      case 'archived':
        filtered = filtered.filter(session => session.metadata?.archived);
        break;
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'updated':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'title':
          return a.title.localeCompare(b.title);
        case 'ideas':
          const aIdeas = getIdeasBySession(a.id)?.length || 0;
          const bIdeas = getIdeasBySession(b.id)?.length || 0;
          return bIdeas - aIdeas;
        default:
          return 0;
      }
    });

    return filtered;
  }, [sessionList, searchQuery, filterBy, sortBy, getIdeasBySession]);

  const handleSessionAction = (action: string, session: BrainstormSession) => {
    switch (action) {
      case 'resume':
        setCurrentSession(session.id);
        onSessionResume(session);
        break;
      case 'view':
        onSessionSelect(session);
        break;
      case 'star':
        updateSession(session.id, {
          metadata: { ...session.metadata, starred: !session.metadata?.starred }
        });
        break;
      case 'archive':
        updateSession(session.id, {
          metadata: { ...session.metadata, archived: !session.metadata?.archived }
        });
        break;
      case 'export':
        exportSession(session.id);
        break;
      case 'delete':
        if (confirm(`Are you sure you want to delete "${session.title}"?`)) {
          deleteSession(session.id);
        }
        break;
    }
  };

  const SessionCard: React.FC<{ session: BrainstormSession }> = ({ session }) => {
    const ideas = getIdeasBySession(session.id) || [];
    const isActive = session.id === currentSessionId;
    const isStarred = session.metadata?.starred;
    const isArchived = session.metadata?.archived;

    return (
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        whileHover={{ scale: 1.01 }}
        className="group"
      >
        <Card className={cn(
          "cursor-pointer transition-all duration-200 hover:shadow-md",
          isActive && "ring-2 ring-offset-2",
          isArchived && "opacity-60"
        )}
        style={{
          ringColor: isActive ? theme.colors.primary : 'transparent'
        }}>
          <CardContent className="p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 
                    className="font-medium truncate cursor-pointer hover:text-primary"
                    onClick={() => handleSessionAction('view', session)}
                    style={{ color: theme.colors.text }}
                  >
                    {session.title}
                  </h3>
                  {isStarred && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
                  {isActive && <Badge variant="default" className="text-xs">Active</Badge>}
                  {isArchived && <Badge variant="secondary" className="text-xs">Archived</Badge>}
                </div>

                {session.metadata?.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                    {session.metadata.description}
                  </p>
                )}

                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <MessageSquare className="w-3 h-3" />
                    <span>{session.messages?.length || 0} messages</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Lightbulb className="w-3 h-3" />
                    <span>{ideas.length} ideas</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span>{new Date(session.updatedAt).toLocaleDateString()}</span>
                  </div>
                </div>

                {session.tags && session.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {session.tags.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {session.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{session.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}
              </div>

              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSessionAction('resume', session)}
                  className="h-8 w-8 p-0"
                >
                  <Play className="w-4 h-4" />
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleSessionAction('view', session)}>
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleSessionAction('star', session)}>
                      {isStarred ? (
                        <>
                          <StarOff className="w-4 h-4 mr-2" />
                          Unstar
                        </>
                      ) : (
                        <>
                          <Star className="w-4 h-4 mr-2" />
                          Star
                        </>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleSessionAction('archive', session)}>
                      <Archive className="w-4 h-4 mr-2" />
                      {isArchived ? 'Unarchive' : 'Archive'}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleSessionAction('export', session)}>
                      <Download className="w-4 h-4 mr-2" />
                      Export
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => handleSessionAction('delete', session)}
                      className="text-red-600"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  if (compactMode) {
    return (
      <div className={cn("space-y-3", className)}>
        <div className="flex items-center justify-between">
          <h3 className="font-medium" style={{ color: theme.colors.text }}>
            Recent Sessions ({filteredAndSortedSessions.length})
          </h3>
        </div>

        <div className="space-y-2">
          {filteredAndSortedSessions.slice(0, 5).map(session => (
            <SessionCard key={session.id} session={session} />
          ))}
        </div>

        {filteredAndSortedSessions.length > 5 && (
          <Button variant="outline" size="sm" className="w-full">
            View All ({filteredAndSortedSessions.length - 5} more)
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold" style={{ color: theme.colors.text }}>
            Session History
          </h2>
          <p style={{ color: theme.colors.textSecondary }}>
            Manage and resume your brainstorming sessions
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4" 
                style={{ color: theme.colors.textSecondary }} />
              <Input
                placeholder="Search sessions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={filterBy} onValueChange={(value: any) => setFilterBy(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="recent">Recent</SelectItem>
                <SelectItem value="starred">Starred</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="updated">Updated</SelectItem>
                <SelectItem value="created">Created</SelectItem>
                <SelectItem value="title">Title</SelectItem>
                <SelectItem value="ideas">Ideas</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Sessions List */}
      <div className="space-y-3">
        <AnimatePresence>
          {filteredAndSortedSessions.map(session => (
            <SessionCard key={session.id} session={session} />
          ))}
        </AnimatePresence>
      </div>

      {filteredAndSortedSessions.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Clock className="w-12 h-12 mx-auto mb-4" style={{ color: theme.colors.textSecondary }} />
            <h3 className="font-medium mb-2" style={{ color: theme.colors.text }}>
              No sessions found
            </h3>
            <p className="mb-4" style={{ color: theme.colors.textSecondary }}>
              {searchQuery || filterBy !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'Start your first brainstorming session to see it here'
              }
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
