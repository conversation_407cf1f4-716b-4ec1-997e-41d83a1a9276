import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  ArrowRight,
  Copy,
  GitBranch,
  Link2,
  Package,
  Send,
  Workflow,
  FolderOpen,
  Check,
  X,
  Loader2,
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { 
  CrossSessionTransfer, 
  SessionLink,
  createBrainstormingPipeline,
} from '@/lib/cross-session-transfer';
import { Idea, IdeaCluster } from '@/types/brainstorm';

interface SessionTransferProps {
  currentSessionId: string;
  selectedIdeas?: Idea[];
  selectedCluster?: IdeaCluster;
}

export const SessionTransfer: React.FC<SessionTransferProps> = ({
  currentSessionId,
  selectedIdeas = [],
  selectedCluster,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'transfer' | 'link' | 'workflow'>('transfer');
  const [targetSessionId, setTargetSessionId] = useState<string>('');
  const [includeRelated, setIncludeRelated] = useState(false);
  const [isTransferring, setIsTransferring] = useState(false);
  const [linkedSessions, setLinkedSessions] = useState<SessionLink[]>([]);
  const [linkType, setLinkType] = useState<SessionLink['linkType']>('related');
  const [forkName, setForkName] = useState('');
  
  const { toast } = useToast();
  const { sessions, getIdeasBySession, getClustersForSession } = useBrainstormStore();

  // Get available brainstorming sessions (excluding current)
  const availableSessions = Object.values(sessions).filter(s => s.id !== currentSessionId);
  
  // Note: Claude sessions are kept separate from brainstorming sessions
  // This ensures complete isolation between the two session types

  useEffect(() => {
    loadLinkedSessions();
  }, [currentSessionId]);

  const loadLinkedSessions = async () => {
    try {
      const links = await CrossSessionTransfer.getLinkedSessions(currentSessionId);
      setLinkedSessions(links);
    } catch (error) {
      console.error('Failed to load linked sessions:', error);
    }
  };

  const handleTransferIdeas = async () => {
    if (!targetSessionId || selectedIdeas.length === 0) {
      toast({
        message: 'Please select ideas and a target session',
        type: 'error',
      });
      return;
    }

    setIsTransferring(true);
    try {
      await CrossSessionTransfer.transferIdeas(
        selectedIdeas,
        currentSessionId,
        targetSessionId
      );

      toast({
        message: `Transferred ${selectedIdeas.length} ideas successfully`,
        type: 'success',
      });
      setIsOpen(false);
    } catch (error) {
      toast({
        message: 'Failed to transfer ideas',
        type: 'error',
      });
    } finally {
      setIsTransferring(false);
    }
  };

  const handleTransferCluster = async () => {
    if (!targetSessionId || !selectedCluster) {
      toast({
        message: 'Please select a cluster and target session',
        type: 'error',
      });
      return;
    }

    setIsTransferring(true);
    try {
      const ideas = getIdeasBySession(currentSessionId);
      await CrossSessionTransfer.transferCluster(
        selectedCluster,
        ideas,
        currentSessionId,
        targetSessionId
      );

      toast({
        message: 'Cluster transferred successfully',
        type: 'success',
      });
      setIsOpen(false);
    } catch (error) {
      toast({
        message: 'Failed to transfer cluster',
        type: 'error',
      });
    } finally {
      setIsTransferring(false);
    }
  };

  const handleLinkSessions = async () => {
    if (!targetSessionId) {
      toast({
        message: 'Please select a target session',
        type: 'error',
      });
      return;
    }

    try {
      await CrossSessionTransfer.linkSessions(
        currentSessionId,
        targetSessionId,
        linkType
      );

      toast({
        message: 'Sessions linked successfully',
        type: 'success',
      });
      await loadLinkedSessions();
    } catch (error) {
      toast({
        message: 'Failed to link sessions',
        type: 'error',
      });
    }
  };

  const handleForkSession = async () => {
    if (!forkName.trim()) {
      toast({
        message: 'Please enter a name for the forked session',
        type: 'error',
      });
      return;
    }

    setIsTransferring(true);
    try {
      const newSessionId = await CrossSessionTransfer.forkSession(
        currentSessionId,
        forkName,
        {
          ideas: true,
          clusters: true,
          messages: false,
          metadata: true,
        }
      );

      toast({
        message: 'Session forked successfully',
        type: 'success',
      });
      setIsOpen(false);
      // Optionally open the forked session
    } catch (error) {
      toast({
        message: 'Failed to fork session',
        type: 'error',
      });
    } finally {
      setIsTransferring(false);
    }
  };

  const handleTransferToClaude = async (idea: Idea) => {
    // Transfer to Claude sessions is disabled to maintain separation
    toast({
      message: 'Transfer to Claude sessions is disabled. Brainstorming sessions are completely separate.',
      type: 'error',
    });
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
        disabled={selectedIdeas.length === 0 && !selectedCluster}
      >
        <Send className="h-4 w-4 mr-2" />
        Transfer
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Session Data Transfer</DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-4 top-4 p-2"
              onClick={() => setIsOpen(false)}
              aria-label="Close dialog"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="transfer">
                <Package className="h-4 w-4 mr-2" />
                Transfer Data
              </TabsTrigger>
              <TabsTrigger value="link">
                <Link2 className="h-4 w-4 mr-2" />
                Link Sessions
              </TabsTrigger>
              <TabsTrigger value="workflow">
                <Workflow className="h-4 w-4 mr-2" />
                Workflow
              </TabsTrigger>
            </TabsList>

            <TabsContent value="transfer" className="space-y-4">
              <div className="space-y-4">
                {/* Transfer Summary */}
                <Card className="p-4">
                  <h4 className="font-medium mb-2">Transfer Summary</h4>
                  {selectedIdeas.length > 0 && (
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="secondary">{selectedIdeas.length} ideas</Badge>
                      <span className="text-sm text-muted-foreground">selected</span>
                    </div>
                  )}
                  {selectedCluster && (
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{selectedCluster.name}</Badge>
                      <span className="text-sm text-muted-foreground">cluster</span>
                    </div>
                  )}
                </Card>

                {/* Target Selection */}
                <div className="space-y-2">
                  <Label>Target Session</Label>
                  <Select value={targetSessionId} onValueChange={setTargetSessionId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select target session" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="new">
                        <div className="flex items-center gap-2">
                          <FolderOpen className="h-4 w-4" />
                          Create New Session
                        </div>
                      </SelectItem>
                      {availableSessions.map(session => (
                        <SelectItem key={session.id} value={session.id}>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">Brainstorm</Badge>
                            {session.title}
                          </div>
                        </SelectItem>
                      ))}
                      {/* Claude sessions are kept separate from brainstorming sessions */}
                    </SelectContent>
                  </Select>
                </div>

                {/* Transfer Options */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-related"
                    checked={includeRelated}
                    onCheckedChange={(checked) => setIncludeRelated(checked as boolean)}
                  />
                  <Label htmlFor="include-related">
                    Include related ideas
                  </Label>
                </div>

                {/* Quick Actions */}
                {selectedIdeas.length === 1 && (
                  <Card className="p-4">
                    <h4 className="font-medium mb-2">Quick Actions</h4>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTransferToClaude(selectedIdeas[0])}
                      >
                        Send to Active Claude Session
                      </Button>
                    </div>
                  </Card>
                )}
              </div>
            </TabsContent>

            <TabsContent value="link" className="space-y-4">
              <div className="space-y-4">
                {/* Link Type Selection */}
                <div className="space-y-2">
                  <Label>Link Type</Label>
                  <Select 
                    value={linkType} 
                    onValueChange={(v) => setLinkType(v as SessionLink['linkType'])}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="parent-child">Parent-Child</SelectItem>
                      <SelectItem value="related">Related</SelectItem>
                      <SelectItem value="continuation">Continuation</SelectItem>
                      <SelectItem value="fork">Fork</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Target Session */}
                <div className="space-y-2">
                  <Label>Link to Session</Label>
                  <Select value={targetSessionId} onValueChange={setTargetSessionId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select session to link" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableSessions.map(session => (
                        <SelectItem key={session.id} value={session.id}>
                          {session.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Existing Links */}
                {linkedSessions.length > 0 && (
                  <Card className="p-4">
                    <h4 className="font-medium mb-2">Linked Sessions</h4>
                    <ScrollArea className="h-32">
                      <div className="space-y-2">
                        {linkedSessions.map(link => (
                          <div key={link.id} className="flex items-center justify-between">
                            <span className="text-sm">
                              {link.link_type} → {
                                sessions.find(s => 
                                  s.id === (link.source_session_id === currentSessionId 
                                    ? link.target_session_id 
                                    : link.source_session_id)
                                )?.title || 'Unknown'
                              }
                            </span>
                            <Badge variant="outline" className="text-xs">
                              {new Date(link.created_at).toLocaleDateString()}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </Card>
                )}
              </div>
            </TabsContent>

            <TabsContent value="workflow" className="space-y-4">
              <div className="space-y-4">
                {/* Fork Session */}
                <Card className="p-4">
                  <h4 className="font-medium mb-2">Fork Session</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Create a copy of this session to explore different directions
                  </p>
                  <div className="space-y-2">
                    <Input
                      placeholder="Fork name"
                      value={forkName}
                      onChange={(e) => setForkName(e.target.value)}
                    />
                    <Button
                      onClick={handleForkSession}
                      disabled={!forkName.trim() || isTransferring}
                      className="w-full"
                    >
                      <GitBranch className="h-4 w-4 mr-2" />
                      Create Fork
                    </Button>
                  </div>
                </Card>

                {/* Pipeline Creation */}
                <Card className="p-4">
                  <h4 className="font-medium mb-2">Create Pipeline</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Set up a multi-stage brainstorming workflow
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      // This would open a pipeline creation dialog
                      toast({
                        message: 'Pipeline creation coming soon',
                        type: 'info',
                      });
                    }}
                    className="w-full"
                  >
                    <Workflow className="h-4 w-4 mr-2" />
                    Design Pipeline
                  </Button>
                </Card>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            {activeTab === 'transfer' && (
              <Button
                onClick={selectedCluster ? handleTransferCluster : handleTransferIdeas}
                disabled={!targetSessionId || isTransferring}
              >
                {isTransferring ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Transferring...
                  </>
                ) : (
                  <>
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Transfer
                  </>
                )}
              </Button>
            )}
            {activeTab === 'link' && (
              <Button
                onClick={handleLinkSessions}
                disabled={!targetSessionId}
              >
                <Link2 className="h-4 w-4 mr-2" />
                Create Link
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};