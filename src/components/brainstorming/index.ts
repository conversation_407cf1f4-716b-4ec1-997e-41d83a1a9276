/**
 * Brainstorming Components Index
 * 
 * This file exports all components related to the enhanced brainstorming system.
 */

// Export VirtualizedIdeaManager as IdeaManager for backward compatibility
export { VirtualizedIdeaManager as IdeaManager } from './VirtualizedIdeaManager';
// Also export with original name
export { VirtualizedIdeaManager } from './VirtualizedIdeaManager';
export { EnhancedBrainstormingChat } from './EnhancedBrainstormingChat';
// Export EnhancedBrainstormingInterface as BrainstormingHub for backward compatibility
export { EnhancedBrainstormingInterface as BrainstormingHub } from './EnhancedBrainstormingInterface';
export { MindMapView } from './MindMapView';
export { KanbanView } from './KanbanView';
export { MatrixView } from './MatrixView';
export { TemplateSelector } from './TemplateSelector';
export { TaskGenerator } from './TaskGenerator';
export { MultiModalInput } from './MultiModalInput';
export { PersonaManager } from './PersonaManager';
export { PersonaCreationInterface } from './PersonaCreationInterface';
export { PersonaSwitcher } from './PersonaSwitcher';
export { BrainstormingSessionHistory } from './BrainstormingSessionHistory';
export { MemoryInsights } from './MemoryInsights';
export { SearchInterface } from './SearchInterface';
export { VoiceInterface } from './VoiceInterface';
export { CollaborationPanel } from './CollaborationPanel';
export { BrainstormErrorBoundary } from './BrainstormErrorBoundary';
export { ClusterManager } from './ClusterManager';
export { ExportManager } from './ExportManager';
export { EnhancedBrainstormingInterface } from './EnhancedBrainstormingInterface';

// Export utility functions
export { formatIdeaStatus, getStatusVariant } from './utils/idea-utils';