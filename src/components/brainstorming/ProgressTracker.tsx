/**
 * Progress Tracker Component
 * 
 * Tracks and visualizes progress through brainstorming sessions including
 * step completion, time tracking, goal achievement, and session metrics.
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircle,
  Circle,
  Clock,
  Target,
  TrendingUp,
  Users,
  Lightbulb,
  MessageSquare,
  BarChart3,
  Timer,
  Play,
  Pause,
  Square,
  RotateCcw,
  Award,
  Flag,
  Activity
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';

interface SessionStep {
  id: string;
  title: string;
  description: string;
  duration: number; // in minutes
  completed: boolean;
  startTime?: string;
  endTime?: string;
  goals: string[];
  metrics: {
    ideasGenerated: number;
    participantContributions: number;
    messagesExchanged: number;
  };
}

interface SessionGoal {
  id: string;
  title: string;
  description: string;
  target: number;
  current: number;
  unit: string;
  priority: 'low' | 'medium' | 'high';
  achieved: boolean;
}

interface SessionMetrics {
  totalDuration: number;
  elapsedTime: number;
  ideasGenerated: number;
  participantCount: number;
  messagesExchanged: number;
  completedSteps: number;
  totalSteps: number;
  goalsAchieved: number;
  totalGoals: number;
}

interface ProgressTrackerProps {
  sessionId: string;
  steps: SessionStep[];
  goals: SessionGoal[];
  metrics: SessionMetrics;
  isActive: boolean;
  onStepComplete: (stepId: string) => void;
  onStepStart: (stepId: string) => void;
  onSessionPause: () => void;
  onSessionResume: () => void;
  onSessionEnd: () => void;
  className?: string;
}

export const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  sessionId,
  steps,
  goals,
  metrics,
  isActive,
  onStepComplete,
  onStepStart,
  onSessionPause,
  onSessionResume,
  onSessionEnd,
  className
}) => {
  const { theme } = useBrainstormingTheme();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [showDetailedMetrics, setShowDetailedMetrics] = useState(false);

  const currentStep = steps[currentStepIndex];
  const progressPercentage = (metrics.completedSteps / metrics.totalSteps) * 100;
  const goalsProgress = (metrics.goalsAchieved / metrics.totalGoals) * 100;

  useEffect(() => {
    if (isActive && currentStep && !currentStep.completed) {
      const interval = setInterval(() => {
        const elapsed = currentStep.startTime 
          ? (Date.now() - new Date(currentStep.startTime).getTime()) / 1000 / 60
          : 0;
        const remaining = Math.max(0, currentStep.duration - elapsed);
        setTimeRemaining(remaining);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isActive, currentStep]);

  const handleStepComplete = () => {
    if (currentStep) {
      onStepComplete(currentStep.id);
      if (currentStepIndex < steps.length - 1) {
        setCurrentStepIndex(currentStepIndex + 1);
        onStepStart(steps[currentStepIndex + 1].id);
      }
    }
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = Math.floor(minutes % 60);
    const secs = Math.floor((minutes % 1) * 60);
    
    if (hours > 0) {
      return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStepStatus = (step: SessionStep, index: number) => {
    if (step.completed) return 'completed';
    if (index === currentStepIndex) return 'active';
    if (index < currentStepIndex) return 'completed';
    return 'pending';
  };

  const getGoalProgress = (goal: SessionGoal) => {
    return Math.min((goal.current / goal.target) * 100, 100);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return theme.colors.error;
      case 'medium': return theme.colors.warning;
      case 'low': return theme.colors.success;
      default: return theme.colors.textSecondary;
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Session Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5" style={{ color: theme.colors.primary }} />
                Session Progress
              </CardTitle>
              <CardDescription>
                Track your brainstorming session progress and metrics
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge 
                variant={isActive ? "default" : "secondary"}
                style={{ 
                  backgroundColor: isActive ? theme.colors.success : theme.colors.textSecondary,
                  color: 'white'
                }}
              >
                {isActive ? 'Active' : 'Paused'}
              </Badge>
              
              {isActive ? (
                <Button variant="outline" size="sm" onClick={onSessionPause}>
                  <Pause className="w-4 h-4 mr-2" />
                  Pause
                </Button>
              ) : (
                <Button variant="outline" size="sm" onClick={onSessionResume}>
                  <Play className="w-4 h-4 mr-2" />
                  Resume
                </Button>
              )}
              
              <Button variant="outline" size="sm" onClick={onSessionEnd}>
                <Square className="w-4 h-4 mr-2" />
                End
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold" style={{ color: theme.colors.primary }}>
                {Math.round(progressPercentage)}%
              </div>
              <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                Overall Progress
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold" style={{ color: theme.colors.primary }}>
                {formatTime(metrics.elapsedTime)}
              </div>
              <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                Time Elapsed
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold" style={{ color: theme.colors.primary }}>
                {metrics.ideasGenerated}
              </div>
              <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                Ideas Generated
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold" style={{ color: theme.colors.primary }}>
                {metrics.participantCount}
              </div>
              <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                Participants
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span style={{ color: theme.colors.text }}>Session Progress</span>
                <span style={{ color: theme.colors.textSecondary }}>
                  {metrics.completedSteps} of {metrics.totalSteps} steps
                </span>
              </div>
              <Progress value={progressPercentage} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span style={{ color: theme.colors.text }}>Goals Achievement</span>
                <span style={{ color: theme.colors.textSecondary }}>
                  {metrics.goalsAchieved} of {metrics.totalGoals} goals
                </span>
              </div>
              <Progress value={goalsProgress} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Step */}
      {currentStep && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Timer className="w-5 h-5" style={{ color: theme.colors.accent }} />
              Current Step: {currentStep.title}
            </CardTitle>
            <CardDescription>{currentStep.description}</CardDescription>
          </CardHeader>
          
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <div className="text-center">
                  <div className="text-lg font-bold" style={{ color: theme.colors.accent }}>
                    {formatTime(timeRemaining)}
                  </div>
                  <div className="text-xs" style={{ color: theme.colors.textSecondary }}>
                    Time Remaining
                  </div>
                </div>
                
                <Separator orientation="vertical" className="h-8" />
                
                <div className="text-center">
                  <div className="text-lg font-bold" style={{ color: theme.colors.accent }}>
                    {currentStep.metrics.ideasGenerated}
                  </div>
                  <div className="text-xs" style={{ color: theme.colors.textSecondary }}>
                    Ideas This Step
                  </div>
                </div>
              </div>
              
              <Button 
                onClick={handleStepComplete}
                disabled={currentStep.completed}
                style={{ backgroundColor: theme.colors.success }}
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Complete Step
              </Button>
            </div>

            <div className="mb-4">
              <div className="flex justify-between text-sm mb-1">
                <span style={{ color: theme.colors.text }}>Step Progress</span>
                <span style={{ color: theme.colors.textSecondary }}>
                  {Math.round(((currentStep.duration - timeRemaining) / currentStep.duration) * 100)}%
                </span>
              </div>
              <Progress 
                value={((currentStep.duration - timeRemaining) / currentStep.duration) * 100} 
                className="h-2" 
              />
            </div>

            {currentStep.goals.length > 0 && (
              <div>
                <h4 className="font-medium mb-2" style={{ color: theme.colors.text }}>
                  Step Goals:
                </h4>
                <div className="space-y-1">
                  {currentStep.goals.map((goal, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <Target className="w-3 h-3" style={{ color: theme.colors.success }} />
                      <span>{goal}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Steps Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Flag className="w-5 h-5" style={{ color: theme.colors.primary }} />
            Session Timeline
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-3">
            {steps.map((step, index) => {
              const status = getStepStatus(step, index);
              
              return (
                <motion.div
                  key={step.id}
                  className={cn(
                    "flex items-center gap-3 p-3 rounded-lg transition-all",
                    status === 'active' && "ring-2 ring-offset-2"
                  )}
                  style={{
                    backgroundColor: status === 'active' ? theme.colors.primary + '10' : 'transparent',
                    ringColor: status === 'active' ? theme.colors.primary : 'transparent'
                  }}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="flex-shrink-0">
                    {status === 'completed' ? (
                      <CheckCircle className="w-6 h-6" style={{ color: theme.colors.success }} />
                    ) : status === 'active' ? (
                      <motion.div
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 1, repeat: Infinity }}
                      >
                        <Circle className="w-6 h-6" style={{ color: theme.colors.primary }} />
                      </motion.div>
                    ) : (
                      <Circle className="w-6 h-6" style={{ color: theme.colors.textSecondary }} />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 
                        className="font-medium"
                        style={{ 
                          color: status === 'completed' ? theme.colors.success : 
                                 status === 'active' ? theme.colors.primary : 
                                 theme.colors.text 
                        }}
                      >
                        {step.title}
                      </h4>
                      <div className="flex items-center gap-2 text-sm">
                        <Clock className="w-3 h-3" />
                        <span style={{ color: theme.colors.textSecondary }}>
                          {step.duration}m
                        </span>
                      </div>
                    </div>
                    <p className="text-sm mt-1" style={{ color: theme.colors.textSecondary }}>
                      {step.description}
                    </p>
                    
                    {status === 'completed' && step.endTime && (
                      <div className="flex items-center gap-4 mt-2 text-xs">
                        <span style={{ color: theme.colors.success }}>
                          ✓ Completed at {new Date(step.endTime).toLocaleTimeString()}
                        </span>
                        <span style={{ color: theme.colors.textSecondary }}>
                          {step.metrics.ideasGenerated} ideas generated
                        </span>
                      </div>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Goals Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="w-5 h-5" style={{ color: theme.colors.warning }} />
            Session Goals
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            {goals.map(goal => (
              <div key={goal.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium" style={{ color: theme.colors.text }}>
                      {goal.title}
                    </h4>
                    <Badge 
                      variant="outline"
                      style={{ 
                        borderColor: getPriorityColor(goal.priority),
                        color: getPriorityColor(goal.priority)
                      }}
                    >
                      {goal.priority}
                    </Badge>
                    {goal.achieved && (
                      <CheckCircle className="w-4 h-4" style={{ color: theme.colors.success }} />
                    )}
                  </div>
                  <span className="text-sm" style={{ color: theme.colors.textSecondary }}>
                    {goal.current} / {goal.target} {goal.unit}
                  </span>
                </div>
                
                <Progress value={getGoalProgress(goal)} className="h-2" />
                
                <p className="text-sm" style={{ color: theme.colors.textSecondary }}>
                  {goal.description}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Metrics */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" style={{ color: theme.colors.primary }} />
              Session Metrics
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetailedMetrics(!showDetailedMetrics)}
            >
              {showDetailedMetrics ? 'Hide' : 'Show'} Details
            </Button>
          </div>
        </CardHeader>
        
        <AnimatePresence>
          {showDetailedMetrics && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="text-center p-3 rounded-lg" style={{ backgroundColor: theme.colors.primary + '10' }}>
                    <Lightbulb className="w-6 h-6 mx-auto mb-2" style={{ color: theme.colors.primary }} />
                    <div className="text-lg font-bold" style={{ color: theme.colors.primary }}>
                      {metrics.ideasGenerated}
                    </div>
                    <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                      Total Ideas
                    </div>
                  </div>
                  
                  <div className="text-center p-3 rounded-lg" style={{ backgroundColor: theme.colors.accent + '10' }}>
                    <MessageSquare className="w-6 h-6 mx-auto mb-2" style={{ color: theme.colors.accent }} />
                    <div className="text-lg font-bold" style={{ color: theme.colors.accent }}>
                      {metrics.messagesExchanged}
                    </div>
                    <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                      Messages
                    </div>
                  </div>
                  
                  <div className="text-center p-3 rounded-lg" style={{ backgroundColor: theme.colors.success + '10' }}>
                    <TrendingUp className="w-6 h-6 mx-auto mb-2" style={{ color: theme.colors.success }} />
                    <div className="text-lg font-bold" style={{ color: theme.colors.success }}>
                      {Math.round((metrics.ideasGenerated / metrics.elapsedTime) * 60) || 0}
                    </div>
                    <div className="text-sm" style={{ color: theme.colors.textSecondary }}>
                      Ideas/Hour
                    </div>
                  </div>
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
};
