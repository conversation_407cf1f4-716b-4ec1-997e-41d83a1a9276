import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/test/utils';
import { IdeaManager } from './index';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { mockIdea } from '@/test/utils';

// Mock the store
vi.mock('@/stores/brainstormStore');

describe('IdeaManager', () => {
  const mockStore = {
    ideas: [
      mockIdea({ id: 'idea-1', content: 'First idea', priority: 'high' }),
      mockIdea({ id: 'idea-2', content: 'Second idea', priority: 'medium' }),
    ],
    addIdea: vi.fn(),
    updateIdea: vi.fn(),
    deleteIdea: vi.fn(),
    linkIdeas: vi.fn(),
    getIdeasBySession: vi.fn(() => [
      mockIdea({ id: 'idea-1', content: 'First idea', priority: 'high' }),
      mockIdea({ id: 'idea-2', content: 'Second idea', priority: 'medium' }),
    ]),
  };

  beforeEach(() => {
    vi.mocked(useBrainstormStore).mockReturnValue(mockStore as any);
  });

  it('should render ideas list', () => {
    render(<IdeaManager sessionId="session-1" />);
    
    expect(screen.getByText('First idea')).toBeInTheDocument();
    expect(screen.getByText('Second idea')).toBeInTheDocument();
  });

  it('should filter ideas by search term', async () => {
    render(<IdeaManager sessionId="session-1" />);
    
    const searchInput = screen.getByPlaceholderText(/search ideas/i);
    fireEvent.change(searchInput, { target: { value: 'First' } });

    await waitFor(() => {
      expect(screen.getByText('First idea')).toBeInTheDocument();
      expect(screen.queryByText('Second idea')).not.toBeInTheDocument();
    });
  });

  it('should filter ideas by priority', async () => {
    render(<IdeaManager sessionId="session-1" />);
    
    const priorityFilter = screen.getByRole('button', { name: /priority/i });
    fireEvent.click(priorityFilter);
    
    const highPriorityOption = screen.getByText(/high/i);
    fireEvent.click(highPriorityOption);

    await waitFor(() => {
      expect(screen.getByText('First idea')).toBeInTheDocument();
      expect(screen.queryByText('Second idea')).not.toBeInTheDocument();
    });
  });

  it('should add a new idea', async () => {
    render(<IdeaManager sessionId="session-1" />);
    
    const addButton = screen.getByRole('button', { name: /add idea/i });
    fireEvent.click(addButton);

    const input = screen.getByPlaceholderText(/enter your idea/i);
    fireEvent.change(input, { target: { value: 'New test idea' } });
    
    const submitButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockStore.addIdea).toHaveBeenCalledWith(
        expect.objectContaining({
          content: 'New test idea',
          sessionId: 'session-1',
        })
      );
    });
  });

  it('should delete an idea', async () => {
    render(<IdeaManager sessionId="session-1" />);
    
    const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
    fireEvent.click(deleteButtons[0]);

    // Confirm deletion
    const confirmButton = screen.getByRole('button', { name: /confirm/i });
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(mockStore.deleteIdea).toHaveBeenCalledWith('idea-1');
    });
  });

  it('should edit an idea', async () => {
    render(<IdeaManager sessionId="session-1" />);
    
    const editButtons = screen.getAllByRole('button', { name: /edit/i });
    fireEvent.click(editButtons[0]);

    const input = screen.getByDisplayValue('First idea');
    fireEvent.change(input, { target: { value: 'Updated first idea' } });
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockStore.updateIdea).toHaveBeenCalledWith('idea-1', {
        content: 'Updated first idea',
      });
    });
  });

  it('should link ideas', async () => {
    render(<IdeaManager sessionId="session-1" />);
    
    const linkButtons = screen.getAllByRole('button', { name: /link/i });
    fireEvent.click(linkButtons[0]);

    // Select idea to link
    const ideaToLink = screen.getByText('Second idea');
    fireEvent.click(ideaToLink);

    const confirmLinkButton = screen.getByRole('button', { name: /confirm link/i });
    fireEvent.click(confirmLinkButton);

    await waitFor(() => {
      expect(mockStore.linkIdeas).toHaveBeenCalledWith('idea-1', 'idea-2');
    });
  });

  it('should display idea statistics', () => {
    render(<IdeaManager sessionId="session-1" />);
    
    expect(screen.getByText(/total ideas: 2/i)).toBeInTheDocument();
    expect(screen.getByText(/high priority: 1/i)).toBeInTheDocument();
  });
});