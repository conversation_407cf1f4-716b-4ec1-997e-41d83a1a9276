/**
 * Brainstorming Mode Component
 * 
 * Dedicated brainstorming interface completely separate from Claude Code sessions.
 * Provides its own navigation, session management, and visual identity.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  Lightbulb,
  Users,
  Target,
  Palette,
  ArrowLeft,
  Settings,
  Download,
  Upload,
  Plus,
  Search,
  Filter,
  Grid3x3,
  List,
  Calendar,
  Clock,
  Tag,
  Star,
  TrendingUp,
  Zap,
  Activity,
  BarChart3,
  MessageSquare,
  Map,
  Kanban,
  FileText,
  HelpCircle,
  Menu,
  X
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

import { EnhancedBrainstormingInterface } from './EnhancedBrainstormingInterface';
import { BrainstormingSessionManager } from './BrainstormingSessionManager';
import { BrainstormingNavigation } from './BrainstormingNavigation';
import { RealTimeCollaboration } from './RealTimeCollaboration';
import { InteractiveIdeaMap } from './InteractiveIdeaMap';
import { TemplateBasedSessionStarter } from './TemplateBasedSessionStarter';
import { ProgressTracker } from './ProgressTracker';
import { GuidedOnboarding, ContextualHelp } from './GuidedOnboarding';
import { EnhancedExportManager } from './EnhancedExportManager';
import {
  BrainstormingThemeProvider,
  BrainstormingModeIndicator,
  BrainstormingBreadcrumbs,
  BrainstormingThemeSelector
} from './BrainstormingThemeProvider';
import { ModernDashboard } from './enhanced-ui/ModernDashboard';
import { EnhancedSessionInterface } from './enhanced-ui/EnhancedSessionInterface';
import { WarpBackground } from './enhanced-ui/WarpBackground';
import { ModernNavigation } from './enhanced-ui/ModernNavigation';
import { dedicatedBrainstormingSessionManager } from '@/lib/brainstorming/dedicatedSessionManager';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { BrainstormSession, TemplateType, ViewType, Idea } from '@/types/brainstorm';

interface BrainstormingModeProps {
  onExitMode: () => void;
  className?: string;
}

interface ModeStats {
  totalSessions: number;
  totalIdeas: number;
  activeCollaborations: number;
  recentActivity: string;
}

export const BrainstormingMode: React.FC<BrainstormingModeProps> = ({
  onExitMode,
  className
}) => {
  return (
    <BrainstormingThemeProvider defaultTheme="creative">
      <BrainstormingModeContent onExitMode={onExitMode} className={className} />
    </BrainstormingThemeProvider>
  );
};

const BrainstormingModeContent: React.FC<BrainstormingModeProps> = ({
  onExitMode,
  className
}) => {
  const [currentView, setCurrentView] = useState<'dashboard' | 'session' | 'create' | 'collaboration' | 'idea-map' | 'progress' | 'export'>('dashboard');
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterTemplate, setFilterTemplate] = useState<TemplateType | 'all'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [navigationCollapsed, setNavigationCollapsed] = useState(false);
  const [sessionView, setSessionView] = useState<ViewType>('chat');
  const [isVoiceEnabled, setIsVoiceEnabled] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [hasSeenOnboarding, setHasSeenOnboarding] = useState(
    localStorage.getItem('brainstorming_onboarding_completed') === 'true'
  );
  const [stats, setStats] = useState<ModeStats>({
    totalSessions: 0,
    totalIdeas: 0,
    activeCollaborations: 0,
    recentActivity: 'No recent activity'
  });

  const { sessions, currentSessionId } = useBrainstormStore();

  useEffect(() => {
    // Calculate stats
    const sessionList = Object.values(sessions);
    const totalSessions = sessionList.length;
    const totalIdeas = sessionList.reduce((sum, session) => sum + (session.ideas?.length || 0), 0);
    const activeCollaborations = sessionList.filter(session => session.isActive).length;
    const recentActivity = sessionList.length > 0
      ? `Last activity: ${new Date(Math.max(...sessionList.map(s => new Date(s.updatedAt).getTime()))).toLocaleDateString()}`
      : 'No recent activity';

    setStats({
      totalSessions,
      totalIdeas,
      activeCollaborations,
      recentActivity
    });

    // Show onboarding for new users
    if (!hasSeenOnboarding && totalSessions === 0) {
      setShowOnboarding(true);
    }
  }, [sessions, hasSeenOnboarding]);

  const handleSessionSelect = useCallback((session: BrainstormSession) => {
    setSelectedSessionId(session.id);
    setCurrentView('session');
  }, []);

  const handleCreateSession = useCallback(() => {
    setCurrentView('create');
  }, []);

  const handleBackToDashboard = useCallback(() => {
    setCurrentView('dashboard');
    setSelectedSessionId(null);
  }, []);

  const handleIdeaAdd = useCallback((idea: Partial<Idea>) => {
    if (selectedSessionId) {
      const newIdea: Idea = {
        id: Date.now().toString(),
        content: idea.content || '',
        category: idea.category,
        tags: idea.tags || [],
        priority: idea.priority || 'medium',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const session = sessions[selectedSessionId];
      if (session) {
        const updatedSession = {
          ...session,
          ideas: [...(session.ideas || []), newIdea],
          updatedAt: new Date().toISOString()
        };
        
        // Update in store/manager
        dedicatedBrainstormingSessionManager.updateSession(selectedSessionId, updatedSession);
      }
    }
  }, [selectedSessionId, sessions]);

  const handleIdeaUpdate = useCallback((ideaId: string, updates: Partial<Idea>) => {
    if (selectedSessionId) {
      const session = sessions[selectedSessionId];
      if (session) {
        const updatedIdeas = session.ideas?.map(idea => 
          idea.id === ideaId ? { ...idea, ...updates, updatedAt: new Date().toISOString() } : idea
        ) || [];
        
        const updatedSession = {
          ...session,
          ideas: updatedIdeas,
          updatedAt: new Date().toISOString()
        };
        
        dedicatedBrainstormingSessionManager.updateSession(selectedSessionId, updatedSession);
      }
    }
  }, [selectedSessionId, sessions]);

  const handleIdeaDelete = useCallback((ideaId: string) => {
    if (selectedSessionId) {
      const session = sessions[selectedSessionId];
      if (session) {
        const updatedIdeas = session.ideas?.filter(idea => idea.id !== ideaId) || [];
        
        const updatedSession = {
          ...session,
          ideas: updatedIdeas,
          updatedAt: new Date().toISOString()
        };
        
        dedicatedBrainstormingSessionManager.updateSession(selectedSessionId, updatedSession);
      }
    }
  }, [selectedSessionId, sessions]);

  const handleSessionUpdate = useCallback((updates: Partial<BrainstormSession>) => {
    if (selectedSessionId) {
      const session = sessions[selectedSessionId];
      if (session) {
        const updatedSession = {
          ...session,
          ...updates,
          updatedAt: new Date().toISOString()
        };
        
        dedicatedBrainstormingSessionManager.updateSession(selectedSessionId, updatedSession);
      }
    }
  }, [selectedSessionId, sessions]);

  const handleNavigate = (viewId: string) => {
    setCurrentView(viewId as any);
  };

  const handleQuickAction = (actionId: string) => {
    switch (actionId) {
      case 'new-session':
        setCurrentView('create');
        break;
      case 'collaboration':
        setCurrentView('collaboration');
        break;
      case 'idea-map':
        setCurrentView('idea-map');
        break;
      case 'progress':
        setCurrentView('progress');
        break;
      case 'export':
        setCurrentView('export');
        break;
      case 'help':
        setShowOnboarding(true);
        break;
      case 'settings':
        // Handle settings
        break;
      case 'import':
        // Handle import
        break;
      default:
        console.log('Quick action:', actionId);
    }
  };

  const handleOnboardingComplete = () => {
    setShowOnboarding(false);
    setHasSeenOnboarding(true);
    localStorage.setItem('brainstorming_onboarding_completed', 'true');
  };

  const handleOnboardingSkip = () => {
    setShowOnboarding(false);
    setHasSeenOnboarding(true);
    localStorage.setItem('brainstorming_onboarding_completed', 'true');
  };

  const filteredSessions = Object.values(sessions).filter(session => {
    const matchesSearch = !searchQuery ||
      session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesTemplate = filterTemplate === 'all' || session.template === filterTemplate;

    return matchesSearch && matchesTemplate;
  });

  return (
    <div className={cn("min-h-screen", className)}>
      <AnimatePresence mode="wait">
        {currentView === 'dashboard' && (
          <motion.div
            key="dashboard"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="h-full"
          >
            <ModernDashboard
              stats={stats}
              sessions={Object.values(sessions)}
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              filterTemplate={filterTemplate}
              setFilterTemplate={setFilterTemplate}
              viewMode={viewMode}
              setViewMode={setViewMode}
              onCreateSession={handleCreateSession}
              onSessionSelect={handleSessionSelect}
              onExitMode={onExitMode}
            />
          </motion.div>
        )}

        {currentView === 'session' && selectedSessionId && sessions[selectedSessionId] && (
          <motion.div
            key="session"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="h-full"
          >
            <EnhancedSessionInterface
              session={sessions[selectedSessionId]}
              currentView={sessionView}
              onViewChange={setSessionView}
              onIdeaAdd={handleIdeaAdd}
              onIdeaUpdate={handleIdeaUpdate}
              onIdeaDelete={handleIdeaDelete}
              onSessionUpdate={handleSessionUpdate}
              isVoiceEnabled={isVoiceEnabled}
              onVoiceToggle={() => setIsVoiceEnabled(!isVoiceEnabled)}
              isSpeaking={isSpeaking}
              onSpeakToggle={() => setIsSpeaking(!isSpeaking)}
              isFullscreen={isFullscreen}
              onFullscreenToggle={() => setIsFullscreen(!isFullscreen)}
              onBack={handleBackToDashboard}
            />
          </motion.div>
        )}

        {currentView === 'create' && (
          <motion.div
            key="create"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="h-full"
          >
            <WarpBackground intensity="medium" color="purple">
              <div className="min-h-screen p-6">
                <div className="mb-4">
                  <Button
                    variant="ghost"
                    onClick={handleBackToDashboard}
                    className="text-white hover:text-purple-200 hover:bg-white/10"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Dashboard
                  </Button>
                </div>

                <TemplateBasedSessionStarter
                  onSessionCreate={(config) => {
                    console.log('Creating session:', config);
                    handleBackToDashboard();
                  }}
                  onCancel={handleBackToDashboard}
                />
              </div>
            </WarpBackground>
          </motion.div>
        )}

        {currentView === 'collaboration' && (
          <motion.div
            key="collaboration"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="h-full"
          >
            <WarpBackground intensity="low" color="blue">
              <div className="min-h-screen p-6">
                <div className="mb-4">
                  <Button
                    variant="ghost"
                    onClick={handleBackToDashboard}
                    className="text-white hover:text-blue-200 hover:bg-white/10"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Dashboard
                  </Button>
                </div>

                <Card className="h-[600px] bg-white/10 backdrop-blur-md border-white/20">
                  <RealTimeCollaboration
                    sessionId={selectedSessionId || 'demo'}
                    currentUserId="user1"
                    participants={[
                      {
                        id: 'user1',
                        name: 'You',
                        color: '#8B5CF6',
                        role: 'owner',
                        status: 'active',
                        lastSeen: new Date().toISOString(),
                        permissions: { canEdit: true, canInvite: true, canModerate: true }
                      }
                    ]}
                    activities={[]}
                    onInviteParticipant={() => console.log('Invite participant')}
                    onRemoveParticipant={(id) => console.log('Remove participant:', id)}
                    onUpdatePermissions={(id, perms) => console.log('Update permissions:', id, perms)}
                    onSendChatMessage={(msg) => console.log('Send message:', msg)}
                  />
                </Card>
              </div>
            </WarpBackground>
          </motion.div>
        )}

        {currentView === 'idea-map' && (
          <motion.div
            key="idea-map"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="h-full"
          >
            <WarpBackground intensity="medium" color="green">
              <div className="min-h-screen p-6">
                <div className="mb-4">
                  <Button
                    variant="ghost"
                    onClick={handleBackToDashboard}
                    className="text-white hover:text-green-200 hover:bg-white/10"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Dashboard
                  </Button>
                </div>

                <Card className="h-[600px] bg-white/10 backdrop-blur-md border-white/20">
                  <InteractiveIdeaMap
                    ideas={Object.values(sessions).flatMap(s => s.ideas || [])}
                    onIdeaUpdate={(idea) => console.log('Update idea:', idea)}
                    onIdeaDelete={(id) => console.log('Delete idea:', id)}
                    onIdeaCreate={(content, pos) => console.log('Create idea:', content, pos)}
                    onClusterCreate={(ids, title) => console.log('Create cluster:', ids, title)}
                    onConnectionCreate={(from, to, type) => console.log('Create connection:', from, to, type)}
                    className="h-full"
                  />
                </Card>
              </div>
            </WarpBackground>
          </motion.div>
        )}

        {currentView === 'progress' && (
          <motion.div
            key="progress"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="h-full"
          >
            <WarpBackground intensity="low" color="orange">
              <div className="min-h-screen p-6">
                <div className="mb-4">
                  <Button
                    variant="ghost"
                    onClick={handleBackToDashboard}
                    className="text-white hover:text-orange-200 hover:bg-white/10"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Dashboard
                  </Button>
                </div>

                <ProgressTracker
                  sessionId={selectedSessionId || 'demo'}
                  steps={[
                    {
                      id: 'step1',
                      title: 'Problem Definition',
                      description: 'Define the core problem to solve',
                      duration: 15,
                      completed: true,
                      startTime: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
                      endTime: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
                      goals: ['Clear problem statement', 'Stakeholder alignment'],
                      metrics: { ideasGenerated: 3, participantContributions: 5, messagesExchanged: 12 }
                    },
                    {
                      id: 'step2',
                      title: 'Idea Generation',
                      description: 'Brainstorm creative solutions',
                      duration: 30,
                      completed: false,
                      startTime: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
                      goals: ['Generate 20+ ideas', 'Diverse perspectives'],
                      metrics: { ideasGenerated: 8, participantContributions: 12, messagesExchanged: 25 }
                    }
                  ]}
                  goals={[
                    {
                      id: 'goal1',
                      title: 'Generate Ideas',
                      description: 'Create at least 20 unique ideas',
                      target: 20,
                      current: 11,
                      unit: 'ideas',
                      priority: 'high',
                      achieved: false
                    }
                  ]}
                  metrics={{
                    totalDuration: 60,
                    elapsedTime: 20,
                    ideasGenerated: 11,
                    participantCount: 3,
                    messagesExchanged: 37,
                    completedSteps: 1,
                    totalSteps: 2,
                    goalsAchieved: 0,
                    totalGoals: 1
                  }}
                  isActive={true}
                  onStepComplete={(id) => console.log('Complete step:', id)}
                  onStepStart={(id) => console.log('Start step:', id)}
                  onSessionPause={() => console.log('Pause session')}
                  onSessionResume={() => console.log('Resume session')}
                  onSessionEnd={() => console.log('End session')}
                />
              </div>
            </WarpBackground>
          </motion.div>
        )}

        {currentView === 'export' && (
          <motion.div
            key="export"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="h-full"
          >
            <WarpBackground intensity="low" color="cyan">
              <div className="min-h-screen p-6">
                <div className="mb-4">
                  <Button
                    variant="ghost"
                    onClick={handleBackToDashboard}
                    className="text-white hover:text-cyan-200 hover:bg-white/10"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Dashboard
                  </Button>
                </div>

                <EnhancedExportManager
                  session={selectedSessionId ? sessions[selectedSessionId] : Object.values(sessions)[0] || {
                    id: 'demo',
                    title: 'Demo Session',
                    description: 'Demo brainstorming session',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    isActive: false,
                    participants: [],
                    tags: [],
                    goals: []
                  }}
                  ideas={Object.values(sessions).flatMap(s => s.ideas || [])}
                  onExport={async (format, options) => {
                    console.log('Exporting:', format, options);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                  }}
                />
              </div>
            </WarpBackground>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Contextual Help */}
      <ContextualHelp context={currentView as any} />

      {/* Guided Onboarding */}
      {showOnboarding && (
        <GuidedOnboarding
          onComplete={handleOnboardingComplete}
          onSkip={handleOnboardingSkip}
        />
      )}
    </div>
  );
};
