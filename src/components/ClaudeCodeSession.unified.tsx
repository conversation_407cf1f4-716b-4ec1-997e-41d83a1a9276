/**
 * Unified ClaudeCodeSession component
 * 
 * This component merges the original ClaudeCodeSession with the refactored version,
 * providing enhanced session management while maintaining all existing functionality.
 */

import React, { useState, useEffect, useRef, useCallback } from "react";
import { motion } from "framer-motion";
import { 
  ArrowLeft,
  Terminal,
  FolderOpen,
  GitBranch,
  Settings,
  Users,
  Bot,
  PlayCircle,
  PauseCircle,
  Square,
  RefreshCw
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { api, type Session } from "@/lib/api";
import { cn } from "@/lib/utils";
import { open } from "@tauri-apps/plugin-dialog";
import { StreamMessage } from "./StreamMessage";
import { FloatingPromptInput, type FloatingPromptInputRef } from "./FloatingPromptInput";
import { ErrorBoundary } from "./ErrorBoundary";
import { TimelineNavigator } from "./TimelineNavigator";
import { CheckpointSettings } from "./CheckpointSettings";
import { SlashCommandsManager } from "./SlashCommandsManager";
import { EnsembleSessionManager } from "./EnsembleSessionManager";
import { AgentOrchestraPanel } from "./AgentOrchestraPanel";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { WebviewPreview } from "./WebviewPreview";
import type { ClaudeStreamMessage } from "./AgentExecution";
import type { ModelType } from "@/types/session";
import { useVirtualizer } from "@tanstack/react-virtual";

interface ClaudeCodeSessionProps {
  /**
   * Optional session to resume (when clicking from SessionList)
   */
  session?: Session;
  /**
   * Initial project path (for new sessions)
   */
  initialProjectPath?: string;
  /**
   * Callback to go back
   */
  onBack: () => void;
  /**
   * Callback to open hooks configuration
   */
  onProjectSettings?: (projectPath: string) => void;
  /**
   * Optional className for styling
   */
  className?: string;
  /**
   * Enhanced features flags
   */
  features?: {
    enableSessionManager?: boolean;
    enableOrchestra?: boolean;
    enableEnsemble?: boolean;
    enableWebview?: boolean;
  };
}

type ViewMode = 'chat' | 'orchestra' | 'ensemble' | 'settings' | 'timeline';

export const ClaudeCodeSession: React.FC<ClaudeCodeSessionProps> = ({
  session: initialSession,
  initialProjectPath,
  onBack,
  className,
  features = {
    enableSessionManager: false, // Disable for now due to type issues
    enableOrchestra: true,
    enableEnsemble: true,
    enableWebview: true,
  }
}) => {
  // Core state
  const [currentSession, setCurrentSession] = useState<Session | null>(initialSession || null);
  const [projectPath, setProjectPath] = useState<string>(initialProjectPath || "");
  const [viewMode, setViewMode] = useState<ViewMode>('chat');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // UI state
  const [sidebarOpen] = useState(true);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [selectedModel] = useState<ModelType>('sonnet');
  
  // Message handling
  const [messages, setMessages] = useState<ClaudeStreamMessage[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  
  // Refs
  const promptInputRef = useRef<FloatingPromptInputRef>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainer = useRef<HTMLDivElement>(null);
  
  // Session initialization effect
  useEffect(() => {
    initializeSession();
    return () => {
      cleanupSession();
    };
  }, [initialSession, initialProjectPath]);

  const initializeSession = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use original session management for stability
      if (initialSession) {
        setCurrentSession(initialSession);
        setProjectPath(initialSession.project_path || "");
        // Load session history using original API
        try {
          const history = await api.loadSessionHistory(initialSession.id, initialSession.project_id);
          setMessages(history || []);
        } catch (historyError) {
          console.warn("Failed to load session history:", historyError);
          setMessages([]);
        }
      } else if (initialProjectPath) {
        setProjectPath(initialProjectPath);
      }
    } catch (err) {
      console.error("Failed to initialize session:", err);
      setError(err instanceof Error ? err.message : "Failed to initialize session");
    } finally {
      setLoading(false);
    }
  };

  const cleanupSession = async () => {
    try {
      // Cleanup logic if needed
    } catch (err) {
      console.error("Error during session cleanup:", err);
    }
  };

  // Enhanced prompt handling
  const handleSendPrompt = useCallback(async (prompt: string, model?: ModelType) => {
    if (!prompt.trim()) return;

    try {
      setIsStreaming(true);
      setError(null);

      // Use original API
      if (!currentSession && projectPath) {
        // Create new session
        const sessionId = await api.openNewSession(projectPath);
        // Create a minimal session object
        const newSession: Session = {
          id: sessionId,
          project_id: projectPath.replace(/[^a-zA-Z0-9]/g, '_'),
          project_path: projectPath,
          created_at: Date.now(),
        };
        setCurrentSession(newSession);
        // Execute with the new session
        await api.executeClaudeCode(sessionId, prompt, model || selectedModel);
      } else if (currentSession) {
        // Execute using original API
        await api.executeClaudeCode(currentSession.id, prompt, model || selectedModel);
      }
    } catch (err) {
      console.error("Failed to send prompt:", err);
      setError(err instanceof Error ? err.message : "Failed to send prompt");
    } finally {
      setIsStreaming(false);
    }
  }, [currentSession, projectPath, selectedModel]);

  // Helper function to copy message content
  const handleCopyMessage = async (message: ClaudeStreamMessage) => {
    try {
      const content = typeof message.content === 'string' 
        ? message.content 
        : JSON.stringify(message.content);
      await navigator.clipboard.writeText(content);
    } catch (err) {
      console.error("Failed to copy message:", err);
    }
  };

  // Project selection handler
  const handleSelectProject = async () => {
    try {
      const selected = await open({
        directory: true,
        multiple: false,
        title: "Select Project Directory"
      });
      
      if (selected && typeof selected === 'string') {
        setProjectPath(selected);
      }
    } catch (err) {
      console.error("Failed to select project:", err);
      setError("Failed to select project directory");
    }
  };

  // Enhanced session controls
  const handlePauseSession = useCallback(async () => {
    try {
      if (currentSession) {
        await api.cancelClaudeExecution(currentSession.id);
      }
    } catch (err) {
      console.error("Failed to pause session:", err);
    }
  }, [currentSession]);

  const handleTerminateSession = useCallback(async () => {
    try {
      setCurrentSession(null);
      setMessages([]);
      onBack();
    } catch (err) {
      console.error("Failed to terminate session:", err);
    }
  }, [onBack]);

  // Virtual scrolling for messages
  const virtualizer = useVirtualizer({
    count: messages.length,
    getScrollElement: () => scrollContainer.current,
    estimateSize: () => 100,
    overscan: 5,
  });

  // Render message list with virtual scrolling
  const renderMessageList = () => (
    <div 
      ref={scrollContainer}
      className="flex-1 overflow-auto"
      style={{ height: `${virtualizer.getTotalSize()}px` }}
    >
      <div style={{ position: 'relative' }}>
        {virtualizer.getVirtualItems().map((virtualItem) => {
          const message = messages[virtualItem.index];
          return (
            <div
              key={virtualItem.key}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                transform: `translateY(${virtualItem.start}px)`,
              }}
            >
              <StreamMessage
                message={message}
                className=""
                streamMessages={messages}
                onCopy={() => handleCopyMessage(message)}
              />
            </div>
          );
        })}
      </div>
      <div ref={messagesEndRef} />
    </div>
  );

  // Render main chat interface
  const renderChatInterface = () => (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-2">
            <Terminal className="h-5 w-5 text-primary" />
            <div>
              <h1 className="font-semibold">
                {currentSession?.first_message || "Claude Code Session"}
              </h1>
              <p className="text-sm text-muted-foreground">
                {projectPath || "No project selected"}
              </p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Session controls */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={handlePauseSession}
                  disabled={!isStreaming}
                >
                  {isStreaming ? <PauseCircle className="h-4 w-4" /> : <PlayCircle className="h-4 w-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isStreaming ? "Pause execution" : "Resume execution"}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={handleTerminateSession}
                >
                  <Square className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Terminate session</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* View mode toggles */}
          {features.enableOrchestra && (
            <Button 
              variant={viewMode === 'orchestra' ? 'default' : 'ghost'} 
              size="sm"
              onClick={() => setViewMode('orchestra')}
            >
              <Users className="h-4 w-4" />
            </Button>
          )}
          
          {features.enableEnsemble && (
            <Button 
              variant={viewMode === 'ensemble' ? 'default' : 'ghost'} 
              size="sm"
              onClick={() => setViewMode('ensemble')}
            >
              <Bot className="h-4 w-4" />
            </Button>
          )}

          <Button 
            variant={viewMode === 'timeline' ? 'default' : 'ghost'} 
            size="sm"
            onClick={() => setViewMode('timeline')}
          >
            <GitBranch className="h-4 w-4" />
          </Button>

          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => setSettingsOpen(true)}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 flex">
        {/* Messages area */}
        <div className="flex-1 flex flex-col">
          {loading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="flex items-center gap-2">
                <RefreshCw className="h-5 w-5 animate-spin" />
                <span>Loading session...</span>
              </div>
            </div>
          ) : error ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <p className="text-destructive mb-2">{error}</p>
                <Button onClick={() => setError(null)} variant="outline">
                  Try Again
                </Button>
              </div>
            </div>
          ) : !projectPath ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center space-y-4">
                <FolderOpen className="h-16 w-16 mx-auto text-muted-foreground" />
                <div>
                  <h3 className="text-lg font-semibold">No Project Selected</h3>
                  <p className="text-muted-foreground">Choose a project directory to start coding</p>
                </div>
                <Button onClick={handleSelectProject}>
                  <FolderOpen className="h-4 w-4 mr-2" />
                  Select Project
                </Button>
              </div>
            </div>
          ) : (
            renderMessageList()
          )}
        </div>

        {/* Side panels */}
        {sidebarOpen && (
          <div className="w-80 border-l bg-muted/30">
            {viewMode === 'orchestra' && features.enableOrchestra && (
              <AgentOrchestraPanel 
                projectPath={projectPath}
                onClose={() => setViewMode('chat')}
              />
            )}
            {viewMode === 'ensemble' && features.enableEnsemble && (
              <EnsembleSessionManager 
                projectPath={projectPath}
                onBack={() => setViewMode('chat')}
              />
            )}
            {viewMode === 'timeline' && currentSession && (
              <TimelineNavigator 
                sessionId={currentSession.id}
                projectId={currentSession.project_id}
                projectPath={projectPath}
                currentMessageIndex={messages.length - 1}
                onCheckpointSelect={(checkpoint) => {
                  console.log('Checkpoint selected:', checkpoint);
                }}
                onFork={(checkpointId) => {
                  console.log('Fork requested:', checkpointId);
                }}
              />
            )}
          </div>
        )}
      </div>

      {/* Input area */}
      {projectPath && (
        <div className="border-t p-4">
          <FloatingPromptInput
            ref={promptInputRef}
            onSend={handleSendPrompt}
            disabled={loading || isStreaming}
            defaultModel={selectedModel}
          />
        </div>
      )}
    </div>
  );

  // Main render
  return (
    <ErrorBoundary>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className={cn("h-full flex flex-col bg-background", className)}
      >
        {renderChatInterface()}

        {/* Settings dialog */}
        <Dialog open={settingsOpen} onOpenChange={setSettingsOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Session Settings</DialogTitle>
              <DialogDescription>
                Configure your Claude session preferences and project settings.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              <CheckpointSettings 
                sessionId={currentSession?.id || ''}
                projectId={currentSession?.project_id || ''}
                projectPath={projectPath}
              />
              <SlashCommandsManager />
              {features.enableWebview && <WebviewPreview 
                initialUrl="http://localhost:3000"
                onClose={() => setSettingsOpen(false)}
              />}
            </div>
            <DialogFooter>
              <Button onClick={() => setSettingsOpen(false)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </motion.div>
    </ErrorBoundary>
  );
};

export default ClaudeCodeSession;