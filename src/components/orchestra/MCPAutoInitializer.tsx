import React, { useState, useEffect } from "react";
import { invoke } from '@tauri-apps/api/core';
import { listen, type UnlistenFn } from '@tauri-apps/api/event';
import { useToast } from '@/hooks/useToast';
import {
  Server,
  Check,
  X,
  Loader2,
  AlertCircle,
  Play,
  Pause,
  Settings,
  RefreshCw,
  Database,
  FileCode,
  GitBranch,
  Globe,
  Terminal,
  Shield,
  Cpu,
  Network
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import type { OrchestratedAgent } from "../AgentOrchestraPanel";

interface MCPAutoInitializerProps {
  orchestratedAgents: OrchestratedAgent[];
  autoInit: boolean;
  onMCPStatusUpdate: (agentId: string, mcpStatus: Record<string, 'pending' | 'running' | 'error'>) => void;
}

interface MCPServerDefinition {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  command: string;
  args?: string[];
  env?: Record<string, string>;
  requiredBy: string[]; // Agent IDs that require this server
  status: 'stopped' | 'starting' | 'running' | 'error';
  error?: string;
  logs?: string[];
}

// Available MCP servers with their configurations
const MCP_SERVER_CATALOG: Record<string, Omit<MCPServerDefinition, 'id' | 'requiredBy' | 'status'>> = {
  filesystem: {
    name: "Filesystem",
    description: "File system operations and management",
    icon: FileCode,
    command: "mcp-server-filesystem",
    args: ["--root", "."]
  },
  git: {
    name: "Git",
    description: "Git version control operations",
    icon: GitBranch,
    command: "mcp-server-git",
    args: ["--repo", "."]
  },
  database: {
    name: "Database",
    description: "Database connections and queries",
    icon: Database,
    command: "mcp-server-database",
    env: {
      DATABASE_URL: ""
    }
  },
  'http-client': {
    name: "HTTP Client",
    description: "HTTP requests and API testing",
    icon: Globe,
    command: "mcp-server-http"
  },
  terminal: {
    name: "Terminal",
    description: "Terminal command execution",
    icon: Terminal,
    command: "mcp-server-terminal",
    args: ["--shell", "/bin/bash"]
  },
  security: {
    name: "Security Scanner",
    description: "Security analysis and vulnerability scanning",
    icon: Shield,
    command: "mcp-server-security"
  },
  performance: {
    name: "Performance Monitor",
    description: "Performance monitoring and profiling",
    icon: Cpu,
    command: "mcp-server-performance"
  },
  network: {
    name: "Network Tools",
    description: "Network diagnostics and monitoring",
    icon: Network,
    command: "mcp-server-network"
  }
};

export const MCPAutoInitializer: React.FC<MCPAutoInitializerProps> = ({
  orchestratedAgents,
  autoInit,
  onMCPStatusUpdate
}) => {
  const [mcpServers, setMCPServers] = useState<MCPServerDefinition[]>([]);
  const [isInitializing, setIsInitializing] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [healthCheckInterval, setHealthCheckInterval] = useState<NodeJS.Timeout | null>(null);
  const { toast } = useToast();

  // Analyze required MCP servers from all agents
  useEffect(() => {
    const requiredServers = new Map<string, Set<string>>();
    
    orchestratedAgents.forEach(agent => {
      agent.requiredMCPServers.forEach(serverId => {
        if (!requiredServers.has(serverId)) {
          requiredServers.set(serverId, new Set());
        }
        requiredServers.get(serverId)!.add(String(agent.agent.id));
      });
    });

    // Create server definitions
    const servers: MCPServerDefinition[] = Array.from(requiredServers.entries()).map(([serverId, agentIds]) => {
      const catalogEntry = MCP_SERVER_CATALOG[serverId];
      if (!catalogEntry) {
        return {
          id: serverId,
          name: serverId,
          description: "Unknown MCP server",
          icon: Server,
          command: serverId,
          requiredBy: Array.from(agentIds),
          status: 'stopped'
        };
      }

      return {
        id: serverId,
        ...catalogEntry,
        requiredBy: Array.from(agentIds),
        status: 'stopped'
      };
    });

    setMCPServers(servers);

    // Auto-initialize if enabled
    if (autoInit && servers.length > 0) {
      initializeRequiredServers();
    }

    // Check for existing running servers
    checkExistingServers(servers);
  }, [orchestratedAgents, autoInit]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (healthCheckInterval) {
        clearInterval(healthCheckInterval);
      }
    };
  }, [healthCheckInterval]);

  const initializeRequiredServers = async () => {
    setIsInitializing(true);

    try {
      // Start servers in parallel but with controlled concurrency
      const stoppedServers = mcpServers.filter(s => s.status === 'stopped');
      const concurrencyLimit = 3; // Start max 3 servers at once
      
      for (let i = 0; i < stoppedServers.length; i += concurrencyLimit) {
        const batch = stoppedServers.slice(i, i + concurrencyLimit);
        await Promise.allSettled(
          batch.map(server => startMCPServer(server.id))
        );
        
        // Wait a moment between batches to avoid overwhelming the system
        if (i + concurrencyLimit < stoppedServers.length) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    } catch (error) {
      console.error('Failed to initialize servers:', error);
      toast({
        message: 'Some servers failed to initialize',
        type: 'error'
      });
    } finally {
      setIsInitializing(false);
    }
  };

  // Check for existing running servers
  const checkExistingServers = async (servers: MCPServerDefinition[]) => {
    try {
      const serverIds = servers.map(s => s.id);
      const runningServers = await invoke('get_running_mcp_servers', {
        serverIds
      }) as { serverId: string; processId: number; healthy: boolean }[];

      setMCPServers(prev => prev.map(server => {
        const runningServer = runningServers.find(r => r.serverId === server.id);
        if (runningServer) {
          return {
            ...server,
            status: runningServer.healthy ? 'running' : 'error',
            logs: [`[${new Date().toISOString()}] Found existing server (PID: ${runningServer.processId})`]
          };
        }
        return server;
      }));
    } catch (error) {
      console.error('Failed to check existing servers:', error);
    }
  };

  const startMCPServer = async (serverId: string) => {
    setMCPServers(prev => prev.map(server => 
      server.id === serverId ? { ...server, status: 'starting' } : server
    ));

    try {
      const server = mcpServers.find(s => s.id === serverId);
      if (!server) {
        throw new Error(`Server ${serverId} not found`);
      }

      // Start real MCP server process
      const result = await invoke('start_mcp_server', {
        serverId,
        serverConfig: {
          name: server.name,
          command: server.command,
          args: server.args || [],
          env: server.env || {},
          requiredBy: server.requiredBy
        }
      }) as { success: boolean; processId?: number; error?: string };

      if (result.success) {
        setMCPServers(prev => prev.map(s => 
          s.id === serverId ? { 
            ...s, 
            status: 'running',
            logs: [`[${new Date().toISOString()}] Server started successfully (PID: ${result.processId})`]
          } : s
        ));

        // Update agent MCP status
        server.requiredBy.forEach(agentId => {
          const agent = orchestratedAgents.find(a => a.agent.id !== undefined && String(a.agent.id) === agentId);
          if (agent) {
            onMCPStatusUpdate(String(agentId), {
              ...agent.mcpStatus,
              [serverId]: 'running'
            });
          }
        });

        toast({
          message: `MCP server ${server.name} started successfully`,
          type: 'success'
        });
      } else {
        throw new Error(result.error || 'Failed to start server');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start server';
      
      setMCPServers(prev => prev.map(server => 
        server.id === serverId ? { 
          ...server, 
          status: 'error',
          error: errorMessage,
          logs: [...(server.logs || []), `[${new Date().toISOString()}] Error: ${errorMessage}`]
        } : server
      ));

      // Update agent MCP status
      const server = mcpServers.find(s => s.id === serverId);
      if (server) {
        server.requiredBy.forEach(agentId => {
          const agent = orchestratedAgents.find(a => a.agent.id !== undefined && String(a.agent.id) === agentId);
          if (agent) {
            onMCPStatusUpdate(agentId, {
              ...agent.mcpStatus,
              [serverId]: 'error'
            });
          }
        });
      }

      toast({
        message: `Failed to start MCP server: ${errorMessage}`,
        type: 'error'
      });
    }
  };

  const stopMCPServer = async (serverId: string) => {
    try {
      const result = await invoke('stop_mcp_server', {
        serverId
      }) as { success: boolean; error?: string };

      if (result.success) {
        setMCPServers(prev => prev.map(server => 
          server.id === serverId ? { 
            ...server, 
            status: 'stopped',
            logs: [...(server.logs || []), `[${new Date().toISOString()}] Server stopped successfully`]
          } : server
        ));

        // Update agent MCP status
        const server = mcpServers.find(s => s.id === serverId);
        if (server) {
          server.requiredBy.forEach(agentId => {
            const agent = orchestratedAgents.find(a => a.agent.id !== undefined && String(a.agent.id) === agentId);
            if (agent) {
              onMCPStatusUpdate(agentId, {
                ...agent.mcpStatus,
                [serverId]: 'pending'
              });
            }
          });
        }

        toast({
          message: `MCP server stopped successfully`,
          type: 'success'
        });
      } else {
        throw new Error(result.error || 'Failed to stop server');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to stop server';
      
      setMCPServers(prev => prev.map(server => 
        server.id === serverId ? { 
          ...server, 
          error: errorMessage,
          logs: [...(server.logs || []), `[${new Date().toISOString()}] Stop error: ${errorMessage}`]
        } : server
      ));

      toast({
        message: `Failed to stop MCP server: ${errorMessage}`,
        type: 'error'
      });
    }
  };

  const restartMCPServer = async (serverId: string) => {
    try {
      await stopMCPServer(serverId);
      // Wait a moment before restarting
      await new Promise(resolve => setTimeout(resolve, 1000));
      await startMCPServer(serverId);
    } catch (error) {
      console.error('Failed to restart MCP server:', error);
    }
  };

  // Health monitoring
  useEffect(() => {
    // Set up health monitoring for running servers
    const interval = setInterval(async () => {
      const runningServers = mcpServers.filter(s => s.status === 'running');
      
      if (runningServers.length > 0) {
        try {
          const healthResults = await invoke('check_mcp_servers_health', {
            serverIds: runningServers.map(s => s.id)
          }) as { serverId: string; healthy: boolean; error?: string }[];

          setMCPServers(prev => prev.map(server => {
            const healthResult = healthResults.find(r => r.serverId === server.id);
            if (healthResult && !healthResult.healthy) {
              // Mark as error if health check failed
              return {
                ...server,
                status: 'error',
                error: healthResult.error || 'Health check failed',
                logs: [...(server.logs || []), `[${new Date().toISOString()}] Health check failed: ${healthResult.error || 'Unknown error'}`]
              };
            }
            return server;
          }));
        } catch (error) {
          console.error('Health check failed:', error);
        }
      }
    }, 30000); // Check every 30 seconds

    setHealthCheckInterval(interval);

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [mcpServers]);

  // Listen for MCP server events
  useEffect(() => {
    let unlistenFunctions: UnlistenFn[] = [];

    const setupEventListeners = async () => {
      try {
        // Listen for server log events
        const unlistenLogs = await listen('mcp-server-log', (event: any) => {
          const { serverId, message, level } = event.payload;
          
          setMCPServers(prev => prev.map(server => 
            server.id === serverId ? {
              ...server,
              logs: [...(server.logs || []), `[${new Date().toISOString()}] ${level.toUpperCase()}: ${message}`].slice(-50) // Keep last 50 logs
            } : server
          ));
        });

        // Listen for server status changes
        const unlistenStatus = await listen('mcp-server-status', (event: any) => {
          const { serverId, status, error } = event.payload;
          
          setMCPServers(prev => prev.map(server => 
            server.id === serverId ? {
              ...server,
              status,
              error: error || undefined,
              logs: [...(server.logs || []), `[${new Date().toISOString()}] Status changed to: ${status}`]
            } : server
          ));

          // Update agent statuses
          const server = mcpServers.find(s => s.id === serverId);
          if (server) {
            server.requiredBy.forEach(agentId => {
              const agent = orchestratedAgents.find(a => a.agent.id !== undefined && String(a.agent.id) === agentId);
              if (agent) {
                onMCPStatusUpdate(agentId, {
                  ...agent.mcpStatus,
                  [serverId]: status === 'running' ? 'running' : status === 'error' ? 'error' : 'pending'
                });
              }
            });
          }
        });

        unlistenFunctions = [unlistenLogs, unlistenStatus];
      } catch (error) {
        console.error('Failed to setup MCP event listeners:', error);
      }
    };

    setupEventListeners();

    return () => {
      unlistenFunctions.forEach(unlisten => unlisten());
    };
  }, [orchestratedAgents, onMCPStatusUpdate]);

  const getStatusIcon = (status: MCPServerDefinition['status']) => {
    switch (status) {
      case 'running': return Check;
      case 'starting': return Loader2;
      case 'error': return X;
      default: return Server;
    }
  };

  const getStatusColor = (status: MCPServerDefinition['status']) => {
    switch (status) {
      case 'running': return 'text-green-500';
      case 'starting': return 'text-blue-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const runningServers = mcpServers.filter(s => s.status === 'running').length;
  const totalServers = mcpServers.length;

  if (mcpServers.length === 0) {
    return (
      <div className="text-center py-4 text-sm text-muted-foreground">
        No MCP servers required
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Summary */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Server className="h-4 w-4" />
          <span className="text-sm font-medium">MCP Servers</span>
          <Badge variant="secondary" className="text-xs">
            {runningServers}/{totalServers} running
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setShowDetails(!showDetails)}
                >
                  <Settings className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Toggle Details</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          {autoInit && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={initializeRequiredServers}
                    disabled={isInitializing}
                  >
                    {isInitializing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Reinitialize All</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>

      {/* Progress */}
      <Progress value={(runningServers / totalServers) * 100} className="h-2" />

      {/* Server List */}
      {showDetails && (
        <div className="space-y-2">
          {mcpServers.map(server => {
            const Icon = server.icon;
            const StatusIcon = getStatusIcon(server.status);
            
            return (
              <Card key={server.id} className={cn(
                "transition-all",
                server.status === 'error' && "border-destructive/50"
              )}>
                <CardHeader className="pb-3 pt-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Icon className="h-4 w-4" />
                      <CardTitle className="text-sm">{server.name}</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {server.id}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <StatusIcon className={cn(
                        "h-4 w-4",
                        getStatusColor(server.status),
                        server.status === 'starting' && "animate-spin"
                      )} />
                      
                      <div className="flex gap-1">
                        {server.status === 'stopped' ? (
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-7 w-7"
                            onClick={() => startMCPServer(server.id)}
                          >
                            <Play className="h-3 w-3" />
                          </Button>
                        ) : server.status === 'running' ? (
                          <>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="h-7 w-7"
                              onClick={() => restartMCPServer(server.id)}
                            >
                              <RefreshCw className="h-3 w-3" />
                            </Button>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="h-7 w-7"
                              onClick={() => stopMCPServer(server.id)}
                            >
                              <Pause className="h-3 w-3" />
                            </Button>
                          </>
                        ) : null}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <p className="text-xs text-muted-foreground mb-2">
                    {server.description}
                  </p>
                  
                  {server.requiredBy.length > 0 && (
                    <div className="text-xs">
                      <span className="text-muted-foreground">Required by: </span>
                      {server.requiredBy.map((agentId, idx) => {
                        const agent = orchestratedAgents.find(a => a.agent.id !== undefined && String(a.agent.id) === agentId);
                        return (
                          <span key={agentId}>
                            {agent?.agent.name || agentId}
                            {idx < server.requiredBy.length - 1 && ", "}
                          </span>
                        );
                      })}
                    </div>
                  )}
                  
                  {server.error && (
                    <Alert variant="destructive" className="mt-2">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription className="text-xs">
                        {server.error}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Auto-init notification */}
      {!autoInit && mcpServers.some(s => s.status === 'stopped') && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle className="text-sm">Manual Initialization Required</AlertTitle>
          <AlertDescription className="text-xs">
            Some MCP servers need to be started manually. Enable auto-initialization in settings.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};
