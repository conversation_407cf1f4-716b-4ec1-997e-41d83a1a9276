/**
 * Core session management types for Claude Code sessions
 */

export type SessionStatus = 'idle' | 'active' | 'streaming' | 'error' | 'terminated';
export type MessageType = 'user' | 'assistant' | 'system' | 'result';
export type MessageStatus = 'pending' | 'streaming' | 'complete' | 'error';
export type ModelType = 'sonnet' | 'opus';
export type ExportFormat = 'jsonl' | 'markdown';

/**
 * Core session metadata
 */
export interface SessionMetadata {
  /** Total token count for the session */
  totalTokens: number;
  /** Number of messages in the session */
  messageCount: number;
  /** Last activity timestamp */
  lastActivity: string;
  /** Session tags for organization */
  tags: string[];
  /** Custom metadata fields */
  custom: Record<string, any>;
}

/**
 * Session settings and preferences
 */
export interface SessionSettings {
  name: any;
  /** Enable automatic checkpoints */
  autoCheckpoint: boolean;
  /** Checkpoint interval in messages */
  checkpointInterval: number;
  /** Maximum messages to keep in memory */
  maxMessages: number;
  /** Enable preview pane */
  previewEnabled: boolean;
  /** Default export format */
  exportFormat: ExportFormat;
  /** Auto-scroll behavior */
  autoScroll: boolean;
}

/**
 * Message content types
 */
export interface TextContent {
  type: 'text';
  text: string;
}

export interface ToolUseContent {
  type: 'tool_use';
  id: string;
  name: string;
  input: Record<string, any>;
}

export interface ToolResultContent {
  type: 'tool_result';
  tool_use_id: string;
  content: string | Record<string, any>;
  is_error?: boolean;
}

export type MessageContent = TextContent | ToolUseContent | ToolResultContent;

/**
 * Message usage statistics
 */
export interface MessageUsage {
  input_tokens: number;
  output_tokens: number;
  cache_creation_input_tokens?: number;
  cache_read_input_tokens?: number;
}

/**
 * Core message structure
 */
export interface ClaudeMessage {
  /** Unique message identifier */
  id: string;
  /** Message type */
  type: MessageType;
  /** Message content array */
  content: MessageContent[];
  /** Message timestamp */
  timestamp: string;
  /** Message status */
  status: MessageStatus;
  /** Token usage information */
  usage?: MessageUsage;
  /** Additional metadata */
  metadata: Record<string, any>;
  /** Error information if status is 'error' */
  error?: string;
}

/**
 * Checkpoint information
 */
export interface Checkpoint {
  /** Unique checkpoint identifier */
  id: string;
  /** Session this checkpoint belongs to */
  sessionId: string;
  /** Human-readable checkpoint name */
  name: string;
  /** Optional description */
  description?: string;
  /** Checkpoint creation timestamp */
  timestamp: string;
  /** Message index at checkpoint */
  messageIndex: number;
  /** Checkpoint metadata */
  metadata: {
    /** Token count at checkpoint */
    tokenCount: number;
    /** File changes since last checkpoint */
    fileChanges: string[];
    /** Custom metadata */
    custom: Record<string, any>;
  };
}

/**
 * Queued prompt information
 */
export interface QueuedPrompt {
  /** Unique prompt identifier */
  id: string;
  /** Prompt text */
  prompt: string;
  /** Model to use */
  model: ModelType;
  /** Queue timestamp */
  timestamp: string;
  /** Priority level (higher = more important) */
  priority: number;
}

/**
 * Core session data structure
 */
export interface SessionData {
  /** Unique session identifier */
  id: string;
  /** Project path */
  projectPath: string;
  /** Project identifier */
  projectId: string;
  /** Session creation timestamp */
  createdAt: string;
  /** Last update timestamp */
  updatedAt: string;
  /** Current session status */
  status: SessionStatus;
  /** Session metadata */
  metadata: SessionMetadata;
  /** Session messages */
  messages: ClaudeMessage[];
  /** Session checkpoints */
  checkpoints: Checkpoint[];
  /** Session settings */
  settings: SessionSettings;
  /** Currently queued prompts */
  queuedPrompts: QueuedPrompt[];
}

/**
 * Session state for UI management
 */
export interface SessionState {
  /** Current session data */
  currentSession: SessionData | null;
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** Streaming state */
  isStreaming: boolean;
  /** Currently streaming message */
  streamingMessage: Partial<ClaudeMessage> | null;
  /** UI visibility states */
  ui: {
    showTimeline: boolean;
    showPreview: boolean;
    showSettings: boolean;
    previewUrl: string;
    queueCollapsed: boolean;
  };
  /** Performance optimization state */
  performance: {
    virtualScrollEnabled: boolean;
    messageCache: Map<string, ClaudeMessage>;
    lastScrollPosition: number;
  };
}

/**
 * Session lifecycle events
 */
export type SessionEvent = 
  | { type: 'session_created'; sessionId: string; projectPath: string }
  | { type: 'session_resumed'; sessionId: string }
  | { type: 'session_terminated'; sessionId: string }
  | { type: 'message_received'; sessionId: string; message: ClaudeMessage }
  | { type: 'message_streaming'; sessionId: string; partial: Partial<ClaudeMessage> }
  | { type: 'checkpoint_created'; sessionId: string; checkpoint: Checkpoint }
  | { type: 'error_occurred'; sessionId: string; error: string };

/**
 * Session event callback type
 */
export type SessionEventCallback = (event: SessionEvent) => void;

/**
 * Unsubscribe function type
 */
export type UnsubscribeFn = () => void;

/**
 * State update callback type
 */
export type StateUpdateCallback = (state: SessionState) => void;